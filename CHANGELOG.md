# 更新日志

本文档记录了 API 网关项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增功能
- **ID Provider 权限控制系统**: 实现了完整的身份提供者管理和权限控制系统
  - 统一的 ID Provider 注册、配置和管理
  - 支持多种认证方式：JWT、OIDC、API Key、mTLS、SAML、LDAP
  - 完整的令牌生命周期管理（验证、刷新、撤销、黑名单）
  - 基于角色的访问控制 (RBAC) 和基于属性的访问控制 (ABAC)
  - 多租户权限隔离和管理
  - 高性能权限缓存机制
  - RESTful 管理 API 接口
  - 认证器工厂模式，支持动态扩展
  - 详细的系统文档、API 文档和部署指南
  - 完整的单元测试覆盖

- **MCP (Model Context Protocol) 转换功能**: 实现了无侵入式的 API 到 MCP 服务转换
  - 完整的 MCP 协议实现（基于 JSON-RPC 2.0）
  - 支持 Request、Response、Notification 三种消息类型
  - 实现协议生命周期管理（初始化、操作、关闭）
  - HTTP 方法到 MCP 方法的映射
  - 请求参数格式转换（路径参数、查询参数、请求体、请求头）
  - 响应数据格式转换
  - 错误状态码映射
  - 支持多种传输方式（stdio、SSE、自定义传输）
  - 灵活的配置管理系统
  - 热重载配置支持
  - 完整的单元测试覆盖
  - 详细的文档和示例代码

### 技术实现
- **ID Provider 权限控制系统**:
  - `pkg/auth/idprovider.go`: ID Provider 管理器实现
  - `pkg/auth/token_manager.go`: 令牌管理系统
  - `pkg/auth/rbac.go`: RBAC 权限控制系统
  - `pkg/auth/admin_api.go`: 管理 API 接口
  - `pkg/auth/factories.go`: 认证器工厂实现
  - `pkg/auth/manager.go`: 集成现有认证管理器
  - `pkg/config/validator.go`: 扩展配置验证器
  - `docs/ID_Provider_权限控制系统.md`: 详细系统文档
  - `docs/API_文档.md`: 管理 API 文档
  - `docs/部署指南.md`: 生产环境部署指南
  - `docs/快速开始.md`: 快速开始指南
  - `configs/gateway-idprovider-example.yaml`: 示例配置文件

- **MCP 转换功能核心组件**:
  - `pkg/mcp/protocol/`: MCP 协议核心实现
    - `jsonrpc.go`: JSON-RPC 2.0 消息处理
    - `session.go`: 会话管理
    - `lifecycle.go`: 生命周期管理
  - `pkg/mcp/converter/`: API 到 MCP 转换器
    - `converter.go`: 转换器管理器
    - `request.go`: 请求转换器
    - `response.go`: 响应转换器
    - `error.go`: 错误转换器
  - `pkg/mcp/config/`: 配置管理系统
    - `config.go`: 配置结构定义
    - `loader.go`: 配置加载器
    - `validator.go`: 配置验证器
  - `pkg/mcp/middleware/`: 中间件实现
    - `middleware.go`: MCP 中间件
  - `pkg/mcp/server/`: MCP 服务器实现
    - `server.go`: MCP 服务器
  - `pkg/mcp/transport/`: 传输层实现
    - `transport.go`: 传输层接口

### 配置文件
- **新增配置文件**:
  - `configs/mcp.yaml`: MCP 转换功能配置文件
  - 支持转换规则配置、参数映射、响应映射、错误映射
  - 支持服务器配置、传输配置、性能配置、日志配置

### 文档更新
- **新增文档**:
  - `pkg/mcp/README.md`: MCP 模块概述文档
  - `docs/mcp-conversion.md`: MCP 转换功能详细文档
  - 包含架构设计、配置说明、使用示例、API 映射规则等

### 示例代码
- **新增示例**:
  - `examples/mcp/basic/main.go`: 基础 MCP 转换示例
  - `examples/mcp/client/mcp_client.go`: MCP 客户端示例
  - 演示如何使用 MCP 转换功能

### 测试覆盖
- **新增测试**:
  - `pkg/mcp/protocol/jsonrpc_test.go`: JSON-RPC 协议测试
  - `pkg/mcp/converter/converter_test.go`: 转换器测试
  - `pkg/mcp/config/validator_test.go`: 配置验证器测试
  - 覆盖核心功能的单元测试

### API 端点
- **新增 MCP 相关端点**:
  - `GET /mcp/sse`: MCP SSE 连接端点
  - `POST /mcp/message`: MCP 消息处理端点
  - `GET /mcp/health`: MCP 健康检查端点
  - `GET /mcp/stats`: MCP 统计信息端点
  - `GET /mcp/config`: MCP 配置信息端点

### 中间件功能
- **MCP 中间件特性**:
  - 自动检测 MCP 转换需求
  - 支持请求头 `X-Convert-To-MCP: true` 触发转换
  - 无侵入式集成，不影响现有 API 功能
  - 完整的错误处理和日志记录
  - 性能指标收集和监控

### 性能优化
- **性能特性**:
  - 支持并发转换控制
  - 内存缓存支持
  - 连接池管理
  - 转换超时控制
  - 详细的性能指标

### 监控和日志
- **监控指标**:
  - `mcp_requests_total`: MCP 请求总数
  - `mcp_conversion_requests_total`: 转换请求总数
  - `mcp_conversion_success_total`: 转换成功总数
  - `mcp_conversion_errors_total`: 转换错误总数
  - `mcp_conversion_duration_seconds`: 转换耗时

### 兼容性
- **向后兼容**:
  - MCP 功能默认禁用，不影响现有功能
  - 可通过配置文件灵活启用和配置
  - 与现有中间件系统完全兼容

## [1.0.0] - 2024-01-01

### 新增功能
- 初始版本发布
- 基础 API 网关功能
- 认证和授权系统
- 流量控制和保护
- 服务发现和负载均衡
- 可观测性支持
- 插件系统

### 安全特性
- JWT 认证
- API 密钥认证
- OAuth/OIDC 集成
- mTLS 支持
- 速率限制
- IP 过滤
- CORS 管理
- Web 应用防火墙

### 协议支持
- HTTP/1.1
- HTTP/2
- WebSocket
- 熔断器
- 重试逻辑
- 舱壁模式

### 可观测性
- Prometheus 指标
- Jaeger 分布式追踪
- 结构化日志
- 审计日志
- 健康检查

### 部署支持
- Docker 容器化
- Kubernetes 部署
- 配置热重载
- 优雅关闭

---

## 版本说明

### 版本号格式
本项目使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 变更类型
- **新增功能** (Added)：新功能
- **变更** (Changed)：对现有功能的变更
- **弃用** (Deprecated)：即将移除的功能
- **移除** (Removed)：已移除的功能
- **修复** (Fixed)：问题修复
- **安全** (Security)：安全相关的修复

### 发布周期
- **主版本**：根据需要发布，包含重大变更
- **次版本**：每月发布，包含新功能和改进
- **修订版本**：根据需要发布，主要是问题修复

### 支持政策
- **当前版本**：完全支持，包含新功能和安全更新
- **前一个主版本**：安全更新和关键问题修复
- **更早版本**：不再维护，建议升级

### 迁移指南
重大版本升级时，我们会提供详细的迁移指南，包括：
- 不兼容变更说明
- 迁移步骤
- 配置文件更新
- API 变更说明
- 最佳实践建议
