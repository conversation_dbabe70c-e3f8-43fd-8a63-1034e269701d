package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"

	"api-gateway/pkg/mcp/config"
	"api-gateway/pkg/mcp/middleware"
	"api-gateway/pkg/telemetry"
)

// Product 产品结构体
type Product struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Price       float64 `json:"price"`
	Category    string  `json:"category"`
	Stock       int     `json:"stock"`
	Available   bool    `json:"available"`
	CreatedAt   string  `json:"created_at"`
	UpdatedAt   string  `json:"updated_at"`
}

// Order 订单结构体
type Order struct {
	ID            int       `json:"id"`
	UserID        int       `json:"user_id"`
	Items         []OrderItem `json:"items"`
	TotalAmount   float64   `json:"total_amount"`
	Status        string    `json:"status"`
	PaymentMethod string    `json:"payment_method"`
	ShippingAddr  string    `json:"shipping_address"`
	CreatedAt     string    `json:"created_at"`
	UpdatedAt     string    `json:"updated_at"`
}

// OrderItem 订单项结构体
type OrderItem struct {
	ProductID int     `json:"product_id"`
	Quantity  int     `json:"quantity"`
	Price     float64 `json:"price"`
}

// 模拟数据
var products = []Product{
	{ID: 1, Name: "笔记本电脑", Description: "高性能笔记本电脑", Price: 5999.99, Category: "电子产品", Stock: 50, Available: true, CreatedAt: "2024-01-01T00:00:00Z", UpdatedAt: "2024-01-01T00:00:00Z"},
	{ID: 2, Name: "智能手机", Description: "最新款智能手机", Price: 3999.99, Category: "电子产品", Stock: 100, Available: true, CreatedAt: "2024-01-01T00:00:00Z", UpdatedAt: "2024-01-01T00:00:00Z"},
	{ID: 3, Name: "无线耳机", Description: "降噪无线耳机", Price: 299.99, Category: "配件", Stock: 200, Available: true, CreatedAt: "2024-01-01T00:00:00Z", UpdatedAt: "2024-01-01T00:00:00Z"},
	{ID: 4, Name: "平板电脑", Description: "轻薄平板电脑", Price: 2999.99, Category: "电子产品", Stock: 0, Available: false, CreatedAt: "2024-01-01T00:00:00Z", UpdatedAt: "2024-01-01T00:00:00Z"},
}

var orders = []Order{
	{ID: 1, UserID: 1, Items: []OrderItem{{ProductID: 1, Quantity: 1, Price: 5999.99}}, TotalAmount: 5999.99, Status: "已完成", PaymentMethod: "信用卡", ShippingAddr: "北京市朝阳区", CreatedAt: "2024-01-15T10:00:00Z", UpdatedAt: "2024-01-15T10:00:00Z"},
	{ID: 2, UserID: 2, Items: []OrderItem{{ProductID: 2, Quantity: 2, Price: 3999.99}}, TotalAmount: 7999.98, Status: "处理中", PaymentMethod: "支付宝", ShippingAddr: "上海市浦东新区", CreatedAt: "2024-01-16T14:30:00Z", UpdatedAt: "2024-01-16T14:30:00Z"},
}

func main() {
	// 创建 Gin 路由器
	router := gin.Default()

	// 创建日志记录器
	logger := &telemetry.Logger{}

	// 创建指标收集器
	metrics := &telemetry.Metrics{}

	// 加载高级 MCP 配置
	mcpConfig := createAdvancedMCPConfig()

	// 创建并添加 MCP 中间件
	mcpMiddleware := middleware.NewMCPMiddleware(mcpConfig, logger, metrics)
	router.Use(mcpMiddleware.Handle())

	// 添加 API 路由
	setupAdvancedAPIRoutes(router)

	// 添加 MCP 管理路由
	setupMCPManagementRoutes(router, mcpMiddleware)

	// 创建 HTTP 服务器
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	// 启动服务器
	go func() {
		fmt.Println("🚀 高级 MCP 示例服务器启动在 http://localhost:8080")
		fmt.Println("📋 支持复杂的 API 到 MCP 转换")
		fmt.Println("🔗 MCP SSE 端点: http://localhost:8080/mcp/sse")
		fmt.Println("📨 MCP 消息端点: http://localhost:8080/mcp/message")
		fmt.Println()
		fmt.Println("高级示例请求:")
		fmt.Println("  产品列表: curl 'http://localhost:8080/api/v1/products?category=电子产品&available=true'")
		fmt.Println("  订单管理: curl 'http://localhost:8080/api/v1/orders?status=处理中&user_id=2'")
		fmt.Println("  MCP 转换: curl -H 'X-Convert-To-MCP: true' 'http://localhost:8080/api/v1/products'")
		fmt.Println()

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("🛑 正在关闭服务器...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭 MCP 中间件
	if err := mcpMiddleware.Shutdown(ctx); err != nil {
		log.Printf("关闭 MCP 中间件失败: %v", err)
	}

	// 关闭 HTTP 服务器
	if err := server.Shutdown(ctx); err != nil {
		log.Printf("关闭服务器失败: %v", err)
	}

	fmt.Println("✅ 服务器已关闭")
}

// createAdvancedMCPConfig 创建高级 MCP 配置
func createAdvancedMCPConfig() *config.MCPConfig {
	cfg := config.DefaultMCPConfig()
	cfg.Enabled = true

	// 高级转换规则
	cfg.ConversionRules = []config.ConversionRule{
		{
			Path:        "/api/v1/products",
			Methods:     []string{"GET", "POST"},
			MCPMethod:   "products.list",
			Enabled:     true,
			Priority:    1,
			Description: "产品列表和创建产品的高级转换",
			ParameterMapping: config.ParameterMapping{
				Query: map[string]string{
					"page":       "pagination.page",
					"limit":      "pagination.limit",
					"category":   "filter.category",
					"price_min":  "filter.price.min",
					"price_max":  "filter.price.max",
					"available":  "filter.available",
					"search":     "filter.search",
					"sort":       "sort.field",
					"order":      "sort.direction",
				},
				Body: map[string]string{
					"name":        "product.name",
					"description": "product.description",
					"price":       "product.price",
					"category":    "product.category",
					"stock":       "product.stock",
					"available":   "product.available",
				},
				Headers: map[string]string{
					"X-Request-ID":   "request.id",
					"X-User-Agent":   "client.user_agent",
					"Authorization":  "auth.token",
				},
			},
			ResponseMapping: config.ResponseMapping{
				Success: map[string]string{
					"data":  "result.products",
					"total": "result.total",
					"page":  "result.pagination.page",
					"limit": "result.pagination.limit",
				},
				Headers: map[string]string{
					"X-Total-Count": "X-MCP-Total-Count",
					"X-Page-Count":  "X-MCP-Page-Count",
				},
			},
			ErrorMapping: config.ErrorMapping{
				StatusCodeMapping: map[int]int{
					400: -32602, // Invalid params
					401: -32001, // Unauthorized
					403: -32002, // Forbidden
					404: -32601, // Method not found
					422: -32602, // Invalid params
					500: -32603, // Internal error
				},
				DefaultErrorCode:    -32603,
				DefaultErrorMessage: "产品服务内部错误",
			},
		},
		{
			Path:        "/api/v1/products/{id}",
			Methods:     []string{"GET", "PUT", "DELETE"},
			MCPMethod:   "products.get",
			Enabled:     true,
			Priority:    2,
			Description: "单个产品的获取、更新和删除操作",
			ParameterMapping: config.ParameterMapping{
				Path: map[string]string{
					"id": "product.id",
				},
				Body: map[string]string{
					"name":        "product.name",
					"description": "product.description",
					"price":       "product.price",
					"category":    "product.category",
					"stock":       "product.stock",
					"available":   "product.available",
				},
			},
		},
		{
			Path:        "/api/v1/orders",
			Methods:     []string{"GET", "POST"},
			MCPMethod:   "orders.list",
			Enabled:     true,
			Priority:    1,
			Description: "订单列表和创建订单的转换",
			ParameterMapping: config.ParameterMapping{
				Query: map[string]string{
					"page":      "pagination.page",
					"limit":     "pagination.limit",
					"status":    "filter.status",
					"user_id":   "filter.user_id",
					"date_from": "filter.date_range.from",
					"date_to":   "filter.date_range.to",
				},
				Body: map[string]string{
					"user_id":          "order.user_id",
					"items":            "order.items",
					"total_amount":     "order.total_amount",
					"payment_method":   "order.payment_method",
					"shipping_address": "order.shipping_address",
				},
			},
		},
		{
			Path:        "/api/v1/orders/{id}",
			Methods:     []string{"GET", "PUT", "DELETE"},
			MCPMethod:   "orders.get",
			Enabled:     true,
			Priority:    2,
			Description: "单个订单的操作",
			ParameterMapping: config.ParameterMapping{
				Path: map[string]string{
					"id": "order.id",
				},
			},
		},
	}

	return cfg
}

// setupAdvancedAPIRoutes 设置高级 API 路由
func setupAdvancedAPIRoutes(router *gin.Engine) {
	api := router.Group("/api/v1")

	// 产品相关路由
	products := api.Group("/products")
	{
		products.GET("", getProductList)
		products.POST("", createProduct)
		products.GET("/:id", getProduct)
		products.PUT("/:id", updateProduct)
		products.DELETE("/:id", deleteProduct)
	}

	// 订单相关路由
	orders := api.Group("/orders")
	{
		orders.GET("", getOrderList)
		orders.POST("", createOrder)
		orders.GET("/:id", getOrder)
		orders.PUT("/:id", updateOrder)
		orders.DELETE("/:id", deleteOrder)
	}

	// 统计相关路由
	stats := api.Group("/stats")
	{
		stats.GET("/products", getProductStats)
		stats.GET("/orders", getOrderStats)
		stats.GET("/revenue", getRevenueStats)
	}

	// 健康检查
	api.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
			"service":   "Advanced MCP API Gateway",
			"features":  []string{"products", "orders", "stats", "mcp"},
		})
	})
}

// setupMCPManagementRoutes 设置 MCP 管理路由
func setupMCPManagementRoutes(router *gin.Engine, mcpMiddleware *middleware.MCPMiddleware) {
	mcp := router.Group("/mcp")

	// MCP 配置管理
	mcp.GET("/config", func(c *gin.Context) {
		config := mcpMiddleware.GetConfig()
		c.JSON(http.StatusOK, gin.H{
			"enabled":          config.Enabled,
			"protocol_version": config.ProtocolVersion,
			"rules":            config.ConversionRules,
			"server":           config.Server,
			"transport":        config.Transport,
			"performance":      config.Performance,
		})
	})

	// MCP 规则管理
	mcp.GET("/rules", func(c *gin.Context) {
		config := mcpMiddleware.GetConfig()
		c.JSON(http.StatusOK, gin.H{
			"total_rules":   len(config.ConversionRules),
			"enabled_rules": countEnabledRules(config.ConversionRules),
			"rules":         config.ConversionRules,
		})
	})

	// MCP 统计信息
	mcp.GET("/stats", func(c *gin.Context) {
		stats := mcpMiddleware.GetStats()
		c.JSON(http.StatusOK, stats)
	})

	// MCP 健康检查
	mcp.GET("/health", func(c *gin.Context) {
		stats := mcpMiddleware.GetStats()
		c.JSON(http.StatusOK, gin.H{
			"status":         "ok",
			"mcp_enabled":    stats.Enabled,
			"protocol_version": stats.ConfigVersion,
			"active_sessions": 0, // 这里可以从服务器获取实际的会话数
			"uptime":         time.Since(time.Now()).String(),
		})
	})
}

// API 处理函数

// getProductList 获取产品列表
func getProductList(c *gin.Context) {
	// 解析查询参数
	category := c.Query("category")
	available := c.Query("available")
	priceMin := c.Query("price_min")
	priceMax := c.Query("price_max")
	search := c.Query("search")

	fmt.Printf("📦 获取产品列表 - category: %s, available: %s, price_min: %s, price_max: %s, search: %s\n",
		category, available, priceMin, priceMax, search)

	// 模拟过滤
	filteredProducts := filterProducts(products, category, available, search)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    filteredProducts,
		"total":   len(filteredProducts),
		"filters": gin.H{
			"category":  category,
			"available": available,
			"search":    search,
		},
	})
}

// createProduct 创建产品
func createProduct(c *gin.Context) {
	var newProduct Product
	if err := c.ShouldBindJSON(&newProduct); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的产品数据",
			"details": err.Error(),
		})
		return
	}

	newProduct.ID = len(products) + 1
	newProduct.CreatedAt = time.Now().Format(time.RFC3339)
	newProduct.UpdatedAt = newProduct.CreatedAt
	products = append(products, newProduct)

	fmt.Printf("➕ 创建产品 - ID: %d, Name: %s, Price: %.2f\n", newProduct.ID, newProduct.Name, newProduct.Price)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    newProduct,
		"message": "产品创建成功",
	})
}

// getProduct 获取单个产品
func getProduct(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("📦 获取产品 - ID: %s\n", id)

	for _, product := range products {
		if fmt.Sprintf("%d", product.ID) == id {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    product,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success":    false,
		"error":      "产品未找到",
		"product_id": id,
	})
}

// updateProduct 更新产品
func updateProduct(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("✏️  更新产品 - ID: %s\n", id)

	var updateData Product
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的产品数据",
			"details": err.Error(),
		})
		return
	}

	for i, product := range products {
		if fmt.Sprintf("%d", product.ID) == id {
			updateData.ID = product.ID
			updateData.CreatedAt = product.CreatedAt
			updateData.UpdatedAt = time.Now().Format(time.RFC3339)
			products[i] = updateData
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    updateData,
				"message": "产品更新成功",
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success":    false,
		"error":      "产品未找到",
		"product_id": id,
	})
}

// deleteProduct 删除产品
func deleteProduct(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("🗑️  删除产品 - ID: %s\n", id)

	for i, product := range products {
		if fmt.Sprintf("%d", product.ID) == id {
			products = append(products[:i], products[i+1:]...)
			c.JSON(http.StatusOK, gin.H{
				"success":    true,
				"message":    "产品删除成功",
				"product_id": id,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success":    false,
		"error":      "产品未找到",
		"product_id": id,
	})
}

// getOrderList 获取订单列表
func getOrderList(c *gin.Context) {
	status := c.Query("status")
	userID := c.Query("user_id")

	fmt.Printf("📋 获取订单列表 - status: %s, user_id: %s\n", status, userID)

	filteredOrders := filterOrders(orders, status, userID)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    filteredOrders,
		"total":   len(filteredOrders),
		"filters": gin.H{
			"status":  status,
			"user_id": userID,
		},
	})
}

// createOrder 创建订单
func createOrder(c *gin.Context) {
	var newOrder Order
	if err := c.ShouldBindJSON(&newOrder); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的订单数据",
			"details": err.Error(),
		})
		return
	}

	newOrder.ID = len(orders) + 1
	newOrder.Status = "待处理"
	newOrder.CreatedAt = time.Now().Format(time.RFC3339)
	newOrder.UpdatedAt = newOrder.CreatedAt
	orders = append(orders, newOrder)

	fmt.Printf("➕ 创建订单 - ID: %d, UserID: %d, Amount: %.2f\n", newOrder.ID, newOrder.UserID, newOrder.TotalAmount)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    newOrder,
		"message": "订单创建成功",
	})
}

// getOrder 获取单个订单
func getOrder(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("📋 获取订单 - ID: %s\n", id)

	for _, order := range orders {
		if fmt.Sprintf("%d", order.ID) == id {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    order,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success":  false,
		"error":    "订单未找到",
		"order_id": id,
	})
}

// updateOrder 更新订单
func updateOrder(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("✏️  更新订单 - ID: %s\n", id)

	var updateData Order
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的订单数据",
			"details": err.Error(),
		})
		return
	}

	for i, order := range orders {
		if fmt.Sprintf("%d", order.ID) == id {
			updateData.ID = order.ID
			updateData.CreatedAt = order.CreatedAt
			updateData.UpdatedAt = time.Now().Format(time.RFC3339)
			orders[i] = updateData
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    updateData,
				"message": "订单更新成功",
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success":  false,
		"error":    "订单未找到",
		"order_id": id,
	})
}

// deleteOrder 删除订单
func deleteOrder(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("🗑️  删除订单 - ID: %s\n", id)

	for i, order := range orders {
		if fmt.Sprintf("%d", order.ID) == id {
			orders = append(orders[:i], orders[i+1:]...)
			c.JSON(http.StatusOK, gin.H{
				"success":  true,
				"message":  "订单删除成功",
				"order_id": id,
			})
			return
		}
	}

	c.JSON(http.StatusNotFound, gin.H{
		"success":  false,
		"error":    "订单未找到",
		"order_id": id,
	})
}

// getProductStats 获取产品统计
func getProductStats(c *gin.Context) {
	totalProducts := len(products)
	availableProducts := 0
	totalValue := 0.0

	for _, product := range products {
		if product.Available {
			availableProducts++
		}
		totalValue += product.Price * float64(product.Stock)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_products":     totalProducts,
			"available_products": availableProducts,
			"total_inventory_value": totalValue,
			"categories": getCategoryStats(),
		},
	})
}

// getOrderStats 获取订单统计
func getOrderStats(c *gin.Context) {
	totalOrders := len(orders)
	totalRevenue := 0.0
	statusStats := make(map[string]int)

	for _, order := range orders {
		totalRevenue += order.TotalAmount
		statusStats[order.Status]++
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_orders":  totalOrders,
			"total_revenue": totalRevenue,
			"status_stats":  statusStats,
		},
	})
}

// getRevenueStats 获取收入统计
func getRevenueStats(c *gin.Context) {
	totalRevenue := 0.0
	for _, order := range orders {
		totalRevenue += order.TotalAmount
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"total_revenue":   totalRevenue,
			"average_order":   totalRevenue / float64(len(orders)),
			"currency":        "CNY",
			"period":          "all_time",
		},
	})
}

// 辅助函数

// filterProducts 过滤产品
func filterProducts(products []Product, category, available, search string) []Product {
	var filtered []Product
	for _, product := range products {
		if category != "" && product.Category != category {
			continue
		}
		if available == "true" && !product.Available {
			continue
		}
		if available == "false" && product.Available {
			continue
		}
		if search != "" && !contains(product.Name, search) && !contains(product.Description, search) {
			continue
		}
		filtered = append(filtered, product)
	}
	return filtered
}

// filterOrders 过滤订单
func filterOrders(orders []Order, status, userID string) []Order {
	var filtered []Order
	for _, order := range orders {
		if status != "" && order.Status != status {
			continue
		}
		if userID != "" && fmt.Sprintf("%d", order.UserID) != userID {
			continue
		}
		filtered = append(filtered, order)
	}
	return filtered
}

// getCategoryStats 获取分类统计
func getCategoryStats() map[string]int {
	stats := make(map[string]int)
	for _, product := range products {
		stats[product.Category]++
	}
	return stats
}

// countEnabledRules 统计启用的规则数量
func countEnabledRules(rules []config.ConversionRule) int {
	count := 0
	for _, rule := range rules {
		if rule.Enabled {
			count++
		}
	}
	return count
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || containsSubstring(s, substr))
}

// containsSubstring 检查字符串是否包含子字符串
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
