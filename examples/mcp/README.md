# MCP (Model Context Protocol) 示例

本目录包含了 API 网关 MCP 转换功能的各种示例，展示了如何使用无侵入式的 API 到 MCP 服务转换。

## 📁 示例目录

### 1. 基础示例 (`basic/`)
最简单的 MCP 转换示例，展示基本的用户管理 API 转换。

**特性：**
- 基本的 CRUD 操作转换
- 简单的参数映射
- 错误处理演示
- MCP 健康检查端点

**运行方式：**
```bash
cd basic
go run main.go
```

**测试命令：**
```bash
# 普通 API 请求
curl http://localhost:8080/api/v1/users

# MCP 转换请求
curl -H "X-Convert-To-MCP: true" http://localhost:8080/api/v1/users

# 直接 MCP 请求
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"users.list","params":{"pagination":{"page":1,"limit":10}}}'

# MCP 健康检查
curl http://localhost:8080/mcp/health
```

### 2. 高级示例 (`advanced/`)
复杂的电商 API 转换示例，展示高级的转换规则和参数映射。

**特性：**
- 复杂的产品和订单管理 API
- 高级参数映射（嵌套结构、数组处理）
- 多种过滤和排序选项
- 统计和分析 API
- MCP 配置管理端点

**运行方式：**
```bash
cd advanced
go run main.go
```

**测试命令：**
```bash
# 产品列表（带过滤）
curl "http://localhost:8080/api/v1/products?category=电子产品&available=true&page=1&limit=5"

# 订单管理
curl "http://localhost:8080/api/v1/orders?status=处理中&user_id=2"

# 创建产品
curl -X POST http://localhost:8080/api/v1/products \
  -H "Content-Type: application/json" \
  -d '{"name":"新产品","description":"测试产品","price":99.99,"category":"测试","stock":100,"available":true}'

# MCP 转换规则查看
curl http://localhost:8080/mcp/rules

# MCP 统计信息
curl http://localhost:8080/mcp/stats
```

### 3. WebSocket 示例 (`websocket/`)
展示 MCP 协议的实时通信能力，包含 WebSocket 和 HTTP API 的混合使用。

**特性：**
- WebSocket 实时通信
- MCP 消息的 WebSocket 传输
- 聊天室功能演示
- 实时消息广播
- Web 界面交互

**运行方式：**
```bash
cd websocket
go run main.go
```

**访问方式：**
- Web 界面: http://localhost:8080
- WebSocket 端点: ws://localhost:8080/ws
- API 端点: http://localhost:8080/api/v1/*

**功能演示：**
1. 打开 http://localhost:8080
2. 点击"连接"建立 WebSocket 连接
3. 在不同标签页测试聊天、MCP 请求、API 调用
4. 观察实时消息传输和转换

### 4. 客户端示例 (`client/`)
MCP 客户端示例，展示如何与 MCP 服务器进行交互。

**特性：**
- 直接 MCP 协议通信
- HTTP 到 MCP 转换测试
- 错误处理演示
- 多种 MCP 方法测试

**运行方式：**
```bash
# 先启动任一服务器示例（如 basic）
cd basic && go run main.go &

# 然后运行客户端
cd ../client
go run mcp_client.go
```

## 🔧 配置说明

### MCP 配置结构
每个示例都包含完整的 MCP 配置，主要包括：

```yaml
mcp:
  enabled: true
  protocol_version: "2024-11-05"
  
  conversion_rules:
    - path: "/api/v1/users"
      methods: ["GET", "POST"]
      mcp_method: "users.list"
      enabled: true
      priority: 1
      parameter_mapping:
        query:
          page: "pagination.page"
          limit: "pagination.limit"
        body:
          name: "user.name"
          email: "user.email"
  
  server:
    name: "API Gateway MCP Server"
    version: "1.0.0"
  
  transport:
    type: "sse"
    sse:
      endpoint: "/mcp/sse"
      post_endpoint: "/mcp/message"
```

### 转换规则说明

1. **路径匹配**: 支持精确匹配和参数匹配（如 `/api/v1/users/{id}`）
2. **方法过滤**: 可指定支持的 HTTP 方法
3. **参数映射**: 支持路径参数、查询参数、请求体、请求头的映射
4. **响应映射**: 支持响应数据和响应头的映射
5. **错误映射**: HTTP 状态码到 MCP 错误码的映射

## 🧪 测试工具

### 1. curl 命令
```bash
# 基本 API 请求
curl http://localhost:8080/api/v1/users

# 启用 MCP 转换
curl -H "X-Convert-To-MCP: true" http://localhost:8080/api/v1/users

# 直接 MCP 请求
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"users.list","params":{}}'
```

### 2. WebSocket 测试
```javascript
// 浏览器控制台
const ws = new WebSocket('ws://localhost:8080/ws', ['mcp']);
ws.onmessage = (event) => console.log(JSON.parse(event.data));
ws.send(JSON.stringify({
  type: 'mcp_request',
  data: {
    method: 'users.list',
    params: { pagination: { page: 1, limit: 10 } }
  }
}));
```

### 3. Postman 集合
可以导入以下 Postman 集合进行测试：

```json
{
  "info": { "name": "MCP API Tests" },
  "item": [
    {
      "name": "Get Users (Normal)",
      "request": {
        "method": "GET",
        "url": "http://localhost:8080/api/v1/users"
      }
    },
    {
      "name": "Get Users (MCP Conversion)",
      "request": {
        "method": "GET",
        "url": "http://localhost:8080/api/v1/users",
        "header": [
          { "key": "X-Convert-To-MCP", "value": "true" }
        ]
      }
    },
    {
      "name": "Direct MCP Request",
      "request": {
        "method": "POST",
        "url": "http://localhost:8080/mcp/message",
        "header": [
          { "key": "Content-Type", "value": "application/json" }
        ],
        "body": {
          "raw": "{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"users.list\",\"params\":{}}"
        }
      }
    }
  ]
}
```

## 📊 监控和调试

### MCP 统计端点
```bash
# 获取 MCP 统计信息
curl http://localhost:8080/mcp/stats

# 获取 MCP 配置信息
curl http://localhost:8080/mcp/config

# 获取转换规则
curl http://localhost:8080/mcp/rules

# 健康检查
curl http://localhost:8080/mcp/health
```

### 日志输出
每个示例都会输出详细的日志信息，包括：
- 请求接收和处理
- MCP 转换过程
- 错误和异常情况
- 性能统计

### 调试技巧
1. **启用详细日志**: 在配置中设置 `logging.level: "debug"`
2. **查看转换统计**: 定期检查 `/mcp/stats` 端点
3. **测试转换规则**: 使用 `X-Convert-To-MCP: true` 头部测试转换
4. **验证 MCP 消息**: 直接发送 JSON-RPC 2.0 格式的消息

## 🚀 部署建议

### 开发环境
```bash
# 克隆项目
git clone <repository>
cd api-gateway/examples/mcp

# 选择示例运行
cd basic  # 或 advanced, websocket
go run main.go
```

### 生产环境
```bash
# 编译
go build -o mcp-server main.go

# 运行
./mcp-server

# 或使用 Docker
docker build -t mcp-server .
docker run -p 8080:8080 mcp-server
```

### 配置文件
生产环境建议使用外部配置文件：
```bash
# 使用自定义配置
export MCP_CONFIG_PATH=/path/to/mcp.yaml
./mcp-server
```

## 🔗 相关资源

- [MCP 协议规范](https://spec.modelcontextprotocol.io/)
- [API 网关文档](../../../docs/)
- [MCP 转换功能文档](../../../docs/mcp-conversion.md)
- [配置参考](../../../configs/mcp.yaml)

## ❓ 常见问题

### Q: 如何添加新的转换规则？
A: 在配置文件的 `conversion_rules` 部分添加新规则，或通过 API 动态添加。

### Q: 转换失败怎么办？
A: 检查日志输出，验证转换规则配置，确保参数映射正确。

### Q: 如何自定义 MCP 方法？
A: 在转换规则中指定 `mcp_method`，并实现相应的处理逻辑。

### Q: 支持哪些传输协议？
A: 目前支持 HTTP SSE、stdio，未来将支持 WebSocket 和自定义传输。

### Q: 如何监控转换性能？
A: 使用 `/mcp/stats` 端点查看统计信息，启用性能日志记录。
