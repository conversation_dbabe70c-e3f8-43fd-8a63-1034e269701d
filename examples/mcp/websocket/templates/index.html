<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            display: flex;
            height: 600px;
        }
        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            padding: 20px;
        }
        .main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fff;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .message.mcp {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .message.system {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        .message.error {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .input-area {
            padding: 20px;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        input, textarea, select, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        input, textarea, select {
            flex: 1;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            min-width: 100px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            text-align: center;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .stats {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .stats h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .stats div {
            margin-bottom: 5px;
            font-size: 14px;
        }
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .tab {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            background: white;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-content {
            display: none;
            padding: 20px;
        }
        .tab-content.active {
            display: block;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 MCP WebSocket 实时通信示例</h1>
            <p>展示 Model Context Protocol 的 WebSocket 实时通信能力</p>
        </div>
        
        <div class="content">
            <div class="sidebar">
                <div id="status" class="status disconnected">
                    🔴 未连接
                </div>
                
                <div class="stats">
                    <h4>📊 连接统计</h4>
                    <div>会话 ID: <span id="sessionId">-</span></div>
                    <div>连接时间: <span id="connectTime">-</span></div>
                    <div>消息数量: <span id="messageCount">0</span></div>
                    <div>MCP 请求: <span id="mcpCount">0</span></div>
                </div>
                
                <div class="input-group">
                    <button id="connectBtn" onclick="connect()">连接</button>
                    <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
                </div>
                
                <div class="input-group">
                    <button onclick="sendPing()">发送 Ping</button>
                    <button onclick="clearMessages()">清空消息</button>
                </div>
            </div>
            
            <div class="main">
                <div class="tabs">
                    <div class="tab active" onclick="switchTab('chat')">💬 聊天</div>
                    <div class="tab" onclick="switchTab('mcp')">🤖 MCP</div>
                    <div class="tab" onclick="switchTab('api')">🔧 API</div>
                </div>
                
                <div class="messages" id="messages"></div>
                
                <div id="chatTab" class="tab-content active">
                    <div class="input-area">
                        <div class="input-group">
                            <input type="text" id="chatMessage" placeholder="输入聊天消息..." onkeypress="handleChatKeyPress(event)">
                            <select id="chatRoom">
                                <option value="general">通用</option>
                                <option value="tech">技术</option>
                                <option value="random">随机</option>
                            </select>
                            <button onclick="sendChatMessage()">发送</button>
                        </div>
                    </div>
                </div>
                
                <div id="mcpTab" class="tab-content">
                    <div class="input-area">
                        <div class="input-group">
                            <select id="mcpMethod">
                                <option value="messages.list">messages.list</option>
                                <option value="rooms.list">rooms.list</option>
                                <option value="users.list">users.list</option>
                                <option value="custom.method">custom.method</option>
                            </select>
                            <button onclick="sendMCPRequest()">发送 MCP 请求</button>
                        </div>
                        <div class="input-group">
                            <textarea id="mcpParams" placeholder='MCP 参数 (JSON 格式)&#10;例如: {"pagination": {"page": 1, "limit": 10}}' rows="3"></textarea>
                        </div>
                    </div>
                </div>
                
                <div id="apiTab" class="tab-content">
                    <div class="input-area">
                        <div class="input-group">
                            <select id="apiMethod">
                                <option value="GET">GET</option>
                                <option value="POST">POST</option>
                                <option value="PUT">PUT</option>
                                <option value="DELETE">DELETE</option>
                            </select>
                            <input type="text" id="apiPath" placeholder="API 路径 (例如: /api/v1/messages)" value="/api/v1/messages">
                            <button onclick="sendAPIRequest()">发送 API 请求</button>
                        </div>
                        <div class="input-group">
                            <textarea id="apiBody" placeholder='请求体 (JSON 格式)&#10;例如: {"content": "Hello", "room": "general"}' rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let sessionId = null;
        let connectTime = null;
        let messageCount = 0;
        let mcpCount = 0;

        function connect() {
            if (ws) {
                return;
            }

            ws = new WebSocket('ws://localhost:8080/ws', ['mcp']);
            
            ws.onopen = function(event) {
                updateStatus(true);
                connectTime = new Date();
                updateStats();
                addMessage('system', '✅ WebSocket 连接已建立');
            };

            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                handleMessage(message);
                messageCount++;
                updateStats();
            };

            ws.onclose = function(event) {
                updateStatus(false);
                addMessage('system', '❌ WebSocket 连接已关闭');
                ws = null;
                sessionId = null;
            };

            ws.onerror = function(error) {
                addMessage('error', '❌ WebSocket 错误: ' + error);
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function updateStatus(connected) {
            const status = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                status.className = 'status connected';
                status.textContent = '🟢 已连接';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                status.className = 'status disconnected';
                status.textContent = '🔴 未连接';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function updateStats() {
            document.getElementById('sessionId').textContent = sessionId || '-';
            document.getElementById('connectTime').textContent = connectTime ? connectTime.toLocaleTimeString() : '-';
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('mcpCount').textContent = mcpCount;
        }

        function handleMessage(message) {
            switch (message.type) {
                case 'welcome':
                    sessionId = message.session_id;
                    addMessage('system', `🎉 欢迎! 会话 ID: ${sessionId}`);
                    break;
                case 'mcp_response':
                    addMessage('mcp', `🤖 MCP 响应: ${JSON.stringify(message.data, null, 2)}`);
                    mcpCount++;
                    break;
                case 'chat_broadcast':
                    addMessage('chat', `💬 ${message.session_id}: ${JSON.stringify(message.data)}`);
                    break;
                case 'pong':
                    addMessage('system', `🏓 Pong 收到`);
                    break;
                default:
                    addMessage('system', `📨 收到消息: ${JSON.stringify(message, null, 2)}`);
            }
            updateStats();
        }

        function addMessage(type, content) {
            const messages = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<div style="font-size: 12px; color: #666; margin-bottom: 5px;">${timestamp}</div>${content}`;
            
            messages.appendChild(messageDiv);
            messages.scrollTop = messages.scrollHeight;
        }

        function sendMessage(message) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify(message));
                return true;
            } else {
                addMessage('error', '❌ WebSocket 未连接');
                return false;
            }
        }

        function sendPing() {
            sendMessage({
                type: 'ping',
                data: { timestamp: Date.now() }
            });
        }

        function sendChatMessage() {
            const content = document.getElementById('chatMessage').value;
            const room = document.getElementById('chatRoom').value;
            
            if (!content.trim()) {
                return;
            }

            sendMessage({
                type: 'chat_message',
                data: {
                    content: content,
                    room: room,
                    user: 'WebUser',
                    timestamp: Date.now()
                }
            });

            document.getElementById('chatMessage').value = '';
        }

        function sendMCPRequest() {
            const method = document.getElementById('mcpMethod').value;
            const paramsText = document.getElementById('mcpParams').value;
            
            let params = null;
            if (paramsText.trim()) {
                try {
                    params = JSON.parse(paramsText);
                } catch (e) {
                    addMessage('error', '❌ MCP 参数 JSON 格式错误: ' + e.message);
                    return;
                }
            }

            sendMessage({
                type: 'mcp_request',
                data: {
                    method: method,
                    params: params
                }
            });
        }

        function sendAPIRequest() {
            const method = document.getElementById('apiMethod').value;
            const path = document.getElementById('apiPath').value;
            const bodyText = document.getElementById('apiBody').value;
            
            let body = null;
            if (bodyText.trim()) {
                try {
                    body = JSON.parse(bodyText);
                } catch (e) {
                    addMessage('error', '❌ API 请求体 JSON 格式错误: ' + e.message);
                    return;
                }
            }

            // 发送 HTTP API 请求
            const url = `http://localhost:8080${path}`;
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Convert-To-MCP': 'true'
                }
            };

            if (body && (method === 'POST' || method === 'PUT')) {
                options.body = JSON.stringify(body);
            }

            fetch(url, options)
                .then(response => response.json())
                .then(data => {
                    addMessage('api', `🔧 API 响应 (${method} ${path}): ${JSON.stringify(data, null, 2)}`);
                })
                .catch(error => {
                    addMessage('error', `❌ API 请求失败: ${error.message}`);
                });
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChatMessage();
            }
        }

        function switchTab(tabName) {
            // 更新标签页
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
            messageCount = 0;
            mcpCount = 0;
            updateStats();
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            updateStats();
        };
    </script>
</body>
</html>
