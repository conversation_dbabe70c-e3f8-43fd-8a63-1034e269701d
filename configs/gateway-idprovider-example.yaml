# API 网关 ID Provider 权限控制示例配置
# 本配置展示了如何配置多种身份提供者和权限控制系统

# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120
  tls:
    enabled: false
    cert_file: "certs/server.crt"
    key_file: "certs/server.key"
    mtls_mode: "request" # none, request, require

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file: "logs/gateway.log"
  max_size: 100
  max_backups: 5
  max_age: 30

# 监控配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090

# 链路追踪配置
tracing:
  enabled: true
  service_name: "api-gateway"
  endpoint: "http://jaeger:14268/api/traces"
  sample_rate: 0.1

# 认证配置 - 支持多种 ID Provider
auth:
  # JWT 认证配置
  jwt:
    enabled: true
    secret: "your-jwt-secret-key-change-in-production"
    algorithm: "HS256"
    expiration: 3600 # 1小时

  # OIDC 认证配置
  oidc:
    enabled: true
    issuer: "https://your-oidc-provider.com"
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    redirect_url: "https://your-gateway.com/auth/callback"
    scopes: ["openid", "profile", "email", "roles"]
    skip_verify: false

  # API Key 认证配置
  api_key:
    enabled: true
    header_name: "X-API-Key"
    query_param: "api_key"

  # mTLS 认证配置
  mtls:
    enabled: false
    ca_file: "certs/ca.crt"
    crl_file: "certs/ca.crl"
    ocsp_enabled: true
    ocsp_timeout_seconds: 10
    verify_client_cert_cn: true
    allowed_cns: ["client1.example.com", "client2.example.com"]
    verify_organization: true
    allowed_organizations: ["Example Corp", "Partner Corp"]

  # 权限策略配置
  policies:
    engine: "builtin" # builtin 或 opa
    opa_path: "/etc/opa/policies"
    policies:
      # 管理员访问策略
      - name: "admin_full_access"
        path: "/admin/*"
        method: "*"
        roles: ["admin", "super_admin"]
        permissions: ["admin:read", "admin:write", "admin:delete"]
        conditions:
          time_range: "00:00-23:59"
          ip_range: "*"

      # API 读取访问策略
      - name: "api_read_access"
        path: "/api/v1/*"
        method: "GET"
        roles: ["user", "admin", "readonly"]
        permissions: ["api:read"]
        conditions:
          time_range: "06:00-22:00"

      # API 写入访问策略
      - name: "api_write_access"
        path: "/api/v1/*"
        method: "POST,PUT,PATCH,DELETE"
        roles: ["user", "admin"]
        permissions: ["api:write"]
        conditions:
          time_range: "08:00-18:00"
          user_attribute: "department=engineering"

      # 健康检查公开访问
      - name: "health_check_public"
        path: "/health"
        method: "GET"
        roles: []
        permissions: []

      # 监控指标访问
      - name: "metrics_access"
        path: "/metrics"
        method: "GET"
        roles: ["monitoring", "admin"]
        permissions: ["metrics:read"]

# 安全配置
security:
  # 限流配置
  rate_limit:
    enabled: true
    algorithm: "token_bucket"
    rules:
      # 全局限流
      - path: "/*"
        method: "*"
        rate: 1000
        burst: 100
        window: 60s
        key_by: "ip"

      # API 限流
      - path: "/api/*"
        method: "*"
        rate: 100
        burst: 20
        window: 60s
        key_by: "user"

      # 管理接口限流
      - path: "/admin/*"
        method: "*"
        rate: 50
        burst: 10
        window: 60s
        key_by: "user"

  # CORS 配置
  cors:
    enabled: true
    allowed_origins: ["https://your-frontend.com", "https://admin.your-domain.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-API-Key"]
    exposed_headers: ["X-Request-ID", "X-Rate-Limit-Remaining"]
    allow_credentials: true
    max_age: 86400

  # WAF 配置
  waf:
    enabled: true
    rules:
      - name: "sql_injection_protection"
        pattern: "(?i)(union|select|insert|delete|update|drop|create|alter|exec|script)"
        action: "block"
        description: "阻止 SQL 注入攻击"

      - name: "xss_protection"
        pattern: "(?i)(<script|javascript:|on\\w+\\s*=)"
        action: "block"
        description: "阻止 XSS 攻击"

      - name: "path_traversal_protection"
        pattern: "(\\.\\./|\\.\\.\\\\)"
        action: "block"
        description: "阻止路径遍历攻击"

  # IP 过滤配置
  ip_filter:
    enabled: false
    whitelist: ["***********/24", "10.0.0.0/8"]
    blacklist: ["*************", "*********"]

# 路由配置
routes:
  # 用户 API 路由
  - name: "user_api"
    path: "/api/v1/users/*"
    method: "*"
    upstream:
      type: "discovery"
      service_name: "user-service"
      load_balancer: "round_robin"
      health_check:
        enabled: true
        path: "/health"
        interval: 30s
        timeout: 5s
        retries: 3
    timeout: 30s
    retries: 3
    headers:
      X-Service: "user-service"
    plugins: ["auth", "rate_limit", "logging"]

  # 订单 API 路由
  - name: "order_api"
    path: "/api/v1/orders/*"
    method: "*"
    upstream:
      type: "discovery"
      service_name: "order-service"
      load_balancer: "weighted_round_robin"
    timeout: 45s
    retries: 2
    plugins: ["auth", "rate_limit", "logging"]

  # 管理 API 路由
  - name: "admin_api"
    path: "/admin/*"
    method: "*"
    upstream:
      type: "static"
      servers:
        - host: "admin-service"
          port: 8080
          weight: 100
      load_balancer: "round_robin"
    timeout: 60s
    retries: 1
    plugins: ["auth", "rate_limit", "logging", "audit"]

# 插件配置
plugins:
  directory: "./plugins"
  plugins:
    # 认证插件
    auth:
      enabled: true
      priority: 100

    # 限流插件
    rate_limit:
      enabled: true
      priority: 200

    # 日志插件
    logging:
      enabled: true
      priority: 300
      config:
        format: "json"
        include_request_body: false
        include_response_body: false

    # 审计插件
    audit:
      enabled: true
      priority: 400
      config:
        log_level: "info"
        include_sensitive_data: false

# 服务发现配置
discovery:
  type: "consul"
  consul:
    address: "consul:8500"
    datacenter: "dc1"
    token: ""

# 缓存配置
cache:
  enabled: true
  type: "memory"
  default_ttl: "5m"
  max_ttl: "1h"
  memory:
    max_size: 104857600 # 100MB
    max_items: 10000
    cleanup_interval: "10m"
    eviction_policy: "lru"

# WebSocket 配置
websocket:
  enabled: true
  connection:
    read_buffer_size: 4096
    write_buffer_size: 4096
    check_origin: true
    allowed_origins: ["https://your-frontend.com"]
    handshake_timeout: "10s"
    read_timeout: "60s"
    write_timeout: "10s"
    ping_interval: "30s"
    pong_timeout: "5s"
    max_message_size: 1048576 # 1MB
  auth:
    enabled: true
    methods: ["jwt", "api_key"]
