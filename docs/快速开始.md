# ID Provider 权限控制系统快速开始指南

## 概述

本指南将帮助您在 5 分钟内快速部署和体验 API 网关的 ID Provider 权限控制系统。

## 前置条件

- Go 1.19+ 已安装
- Git 已安装
- 8080 和 9090 端口可用

## 快速部署

### 1. 获取代码

```bash
# 克隆项目
git clone https://github.com/your-org/api-gateway.git
cd api-gateway

# 安装依赖
go mod download
```

### 2. 编译运行

```bash
# 编译项目
go build -o api-gateway cmd/gateway/main.go

# 使用示例配置启动
./api-gateway -config configs/gateway-idprovider-example.yaml
```

### 3. 验证部署

```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查监控指标
curl http://localhost:9090/metrics
```

如果看到类似以下输出，说明部署成功：

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0"
}
```

## 基础配置

### 1. JWT 认证配置

编辑配置文件，启用 JWT 认证：

```yaml
auth:
  jwt:
    enabled: true
    secret: "your-secret-key-change-in-production"
    algorithm: "HS256"
    expiration: 3600
```

### 2. 创建第一个 ID Provider

使用管理 API 创建 JWT 提供者：

```bash
curl -X POST http://localhost:8080/admin/auth/providers \
  -H "Content-Type: application/json" \
  -d '{
    "id": "jwt-provider-1",
    "name": "JWT 认证提供者",
    "type": "jwt",
    "description": "用于 API 访问的 JWT 认证",
    "priority": 10,
    "enabled": true,
    "config": {
      "secret": "your-secret-key",
      "algorithm": "HS256",
      "expiration": 3600
    }
  }'
```

### 3. 创建权限和角色

```bash
# 创建权限
curl -X POST http://localhost:8080/admin/auth/permissions \
  -H "Content-Type: application/json" \
  -d '{
    "id": "api-read",
    "name": "API 读取权限",
    "description": "读取 API 数据的权限",
    "resource": "api",
    "action": "read"
  }'

# 创建角色
curl -X POST http://localhost:8080/admin/auth/roles \
  -H "Content-Type: application/json" \
  -d '{
    "id": "api-user",
    "name": "API 用户",
    "description": "普通 API 用户角色",
    "permissions": ["api-read"]
  }'
```

### 4. 创建用户

```bash
curl -X POST http://localhost:8080/admin/auth/users \
  -H "Content-Type: application/json" \
  -d '{
    "id": "user001",
    "username": "testuser",
    "email": "<EMAIL>",
    "display_name": "测试用户",
    "roles": ["api-user"],
    "status": "active"
  }'
```

## 测试认证流程

### 1. 生成 JWT Token

创建一个简单的 JWT 生成脚本 `generate_token.go`：

```go
package main

import (
    "fmt"
    "time"
    "github.com/golang-jwt/jwt/v5"
)

func main() {
    // 创建 token
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
        "sub": "user001",
        "username": "testuser",
        "roles": []string{"api-user"},
        "permissions": []string{"api-read"},
        "exp": time.Now().Add(time.Hour).Unix(),
        "iat": time.Now().Unix(),
    })

    // 签名 token
    tokenString, err := token.SignedString([]byte("your-secret-key"))
    if err != nil {
        panic(err)
    }

    fmt.Println("JWT Token:", tokenString)
}
```

运行生成 token：

```bash
go run generate_token.go
```

### 2. 测试 API 访问

```bash
# 使用生成的 token 访问受保护的 API
TOKEN="your-generated-jwt-token"

curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/api/v1/protected-resource
```

### 3. 验证权限检查

```bash
# 检查用户权限
curl -X GET http://localhost:8080/admin/auth/users/user001/permissions

# 测试权限验证
curl -X POST http://localhost:8080/admin/auth/check-permission \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user001",
    "resource": "api",
    "action": "read"
  }'
```

## 配置路由和策略

### 1. 添加受保护的路由

在配置文件中添加路由：

```yaml
routes:
  - name: "protected_api"
    path: "/api/v1/*"
    method: "*"
    upstream:
      type: "static"
      servers:
        - host: "backend-service"
          port: 8081
    plugins: ["auth", "rate_limit"]

  - name: "public_api"
    path: "/public/*"
    method: "*"
    upstream:
      type: "static"
      servers:
        - host: "backend-service"
          port: 8081
    plugins: ["rate_limit"]
```

### 2. 配置权限策略

```yaml
auth:
  policies:
    engine: "builtin"
    policies:
      - name: "api_access"
        path: "/api/v1/*"
        method: "*"
        roles: ["api-user", "admin"]
        permissions: ["api-read"]
        conditions:
          time_range: "00:00-23:59"

      - name: "admin_access"
        path: "/admin/*"
        method: "*"
        roles: ["admin"]
        permissions: ["admin-read", "admin-write"]
```

## 监控和日志

### 1. 查看监控指标

访问 Prometheus 指标端点：

```bash
curl http://localhost:9090/metrics | grep auth
```

常见指标：
- `auth_requests_total`: 认证请求总数
- `auth_success_total`: 认证成功总数
- `auth_failures_total`: 认证失败总数
- `permission_checks_total`: 权限检查总数

### 2. 查看日志

```bash
# 查看实时日志
tail -f logs/gateway.log

# 过滤认证相关日志
grep "auth" logs/gateway.log
```

### 3. 令牌统计

```bash
# 获取令牌统计信息
curl http://localhost:8080/admin/auth/tokens/stats
```

## 常用管理操作

### 1. 管理 ID Provider

```bash
# 列出所有提供者
curl http://localhost:8080/admin/auth/providers

# 启用/禁用提供者
curl -X POST http://localhost:8080/admin/auth/providers/jwt-provider-1/enable
curl -X POST http://localhost:8080/admin/auth/providers/jwt-provider-1/disable

# 测试提供者连接
curl -X POST http://localhost:8080/admin/auth/providers/jwt-provider-1/test
```

### 2. 令牌管理

```bash
# 列出用户的令牌
curl http://localhost:8080/admin/auth/users/user001/tokens

# 撤销用户的所有令牌
curl -X DELETE http://localhost:8080/admin/auth/tokens/user/user001

# 清理过期令牌
curl -X POST http://localhost:8080/admin/auth/tokens/cleanup
```

### 3. 权限管理

```bash
# 为用户分配新角色
curl -X POST http://localhost:8080/admin/auth/users/user001/roles/admin

# 为角色分配新权限
curl -X POST http://localhost:8080/admin/auth/roles/api-user/permissions/api-write

# 检查用户权限
curl http://localhost:8080/admin/auth/users/user001/permissions
```

## 高级配置示例

### 1. OIDC 集成

```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://accounts.google.com"
    client_id: "your-google-client-id"
    client_secret: "your-google-client-secret"
    redirect_url: "http://localhost:8080/auth/callback"
    scopes: ["openid", "profile", "email"]
```

### 2. mTLS 认证

```yaml
auth:
  mtls:
    enabled: true
    ca_file: "certs/ca.crt"
    verify_client_cert_cn: true
    allowed_cns: ["client.example.com"]
```

### 3. 多租户配置

```bash
# 创建租户
curl -X POST http://localhost:8080/admin/auth/tenants \
  -H "Content-Type: application/json" \
  -d '{
    "id": "tenant1",
    "name": "租户1",
    "description": "第一个租户",
    "status": "active"
  }'

# 为租户创建用户
curl -X POST http://localhost:8080/admin/auth/users \
  -H "Content-Type: application/json" \
  -d '{
    "id": "tenant1-user001",
    "username": "tenant1user",
    "tenant_id": "tenant1",
    "roles": ["api-user"]
  }'
```

## 故障排除

### 常见问题

1. **认证失败**
   ```bash
   # 检查 JWT 密钥配置
   curl http://localhost:8080/admin/auth/providers/jwt-provider-1
   
   # 验证 token 格式
   echo "your-jwt-token" | base64 -d
   ```

2. **权限拒绝**
   ```bash
   # 检查用户角色
   curl http://localhost:8080/admin/auth/users/user001/roles
   
   # 检查角色权限
   curl http://localhost:8080/admin/auth/roles/api-user
   ```

3. **服务无法启动**
   ```bash
   # 验证配置文件
   ./api-gateway -config configs/gateway-idprovider-example.yaml -validate
   
   # 检查端口占用
   netstat -tlnp | grep :8080
   ```

### 调试模式

启用调试日志：

```yaml
logging:
  level: "debug"
  format: "text"
  output: "stdout"
```

## 下一步

现在您已经成功部署了 ID Provider 权限控制系统，可以：

1. **阅读详细文档**: 查看 `docs/ID_Provider_权限控制系统.md`
2. **配置生产环境**: 参考 `docs/部署指南.md`
3. **集成现有系统**: 使用管理 API 进行集成
4. **监控和优化**: 设置 Prometheus 和 Grafana 监控
5. **安全加固**: 配置 TLS、限流和 WAF

## 示例应用

我们提供了一个完整的示例应用，展示如何集成 ID Provider 权限控制系统：

```bash
# 运行示例应用
cd examples/todo-app
go run main.go
```

示例应用包含：
- 用户注册和登录
- JWT token 生成和验证
- 基于角色的权限控制
- API 访问控制

## 社区和支持

- **文档**: [完整文档](docs/)
- **示例**: [示例代码](examples/)
- **问题反馈**: [GitHub Issues](https://github.com/your-org/api-gateway/issues)
- **讨论**: [GitHub Discussions](https://github.com/your-org/api-gateway/discussions)

## 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。
