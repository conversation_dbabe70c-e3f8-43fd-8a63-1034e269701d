# ID Provider 权限控制系统

## 概述

本文档介绍了 API 网关中新实现的 ID Provider（身份提供者）权限控制系统。该系统提供了统一的身份认证和授权管理，支持多种认证方式，并实现了完整的 RBAC（基于角色的访问控制）功能。

## 系统架构

### 核心组件

1. **ID Provider 管理器** (`pkg/auth/idprovider.go`)
   - 统一管理多种身份提供者
   - 支持动态注册和配置
   - 认证器工厂模式

2. **令牌管理器** (`pkg/auth/token_manager.go`)
   - 令牌生命周期管理
   - 令牌验证和刷新
   - 黑名单机制

3. **RBAC 管理器** (`pkg/auth/rbac.go`)
   - 权限、角色、用户管理
   - 多租户支持
   - 权限缓存

4. **管理 API** (`pkg/auth/admin_api.go`)
   - RESTful 管理接口
   - 动态配置管理
   - 监控和统计

## 支持的身份提供者

### 1. JWT (JSON Web Token)
- **算法支持**: HS256, HS384, HS512, RS256, RS384, RS512, ES256, ES384, ES512
- **密钥类型**: 对称密钥（HMAC）和非对称密钥（RSA/ECDSA）
- **自定义声明**: 支持自定义 JWT 声明
- **令牌刷新**: 支持访问令牌和刷新令牌

**配置示例**:
```yaml
auth:
  jwt:
    enabled: true
    secret: "your-secret-key"
    algorithm: "HS256"
    expiration: 3600
```

### 2. OIDC (OpenID Connect)
- **标准兼容**: 完全兼容 OpenID Connect 1.0 规范
- **自动发现**: 支持 OIDC Discovery
- **多种流程**: 支持授权码流程
- **用户信息**: 自动获取用户信息和权限

**配置示例**:
```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://your-oidc-provider.com"
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    redirect_url: "https://your-gateway.com/auth/callback"
    scopes: ["openid", "profile", "email", "roles"]
```

### 3. API Key
- **多种来源**: 支持 HTTP 头部和查询参数
- **灵活配置**: 可自定义字段名称
- **权限映射**: 支持 API Key 到权限的映射

**配置示例**:
```yaml
auth:
  api_key:
    enabled: true
    header_name: "X-API-Key"
    query_param: "api_key"
```

### 4. mTLS (Mutual TLS)
- **证书验证**: 完整的客户端证书验证
- **OCSP 检查**: 支持在线证书状态检查
- **CN 验证**: 支持证书 CN 字段验证
- **组织验证**: 支持证书组织字段验证

**配置示例**:
```yaml
auth:
  mtls:
    enabled: true
    ca_file: "certs/ca.crt"
    verify_client_cert_cn: true
    allowed_cns: ["client1.example.com"]
    verify_organization: true
    allowed_organizations: ["Example Corp"]
```

## RBAC 权限控制

### 权限模型

#### 1. 权限 (Permission)
```go
type Permission struct {
    ID          string            // 权限唯一标识
    Name        string            // 权限名称
    Description string            // 权限描述
    Resource    string            // 资源类型
    Action      string            // 操作类型
    Conditions  map[string]string // 权限条件
}
```

#### 2. 角色 (Role)
```go
type Role struct {
    ID          string   // 角色唯一标识
    Name        string   // 角色名称
    Description string   // 角色描述
    Permissions []string // 权限ID列表
    ParentRoles []string // 父角色ID列表（角色继承）
}
```

#### 3. 用户 (User)
```go
type User struct {
    ID          string                 // 用户唯一标识
    Username    string                 // 用户名
    Roles       []string               // 角色ID列表
    Permissions []string               // 直接分配的权限ID列表
    Attributes  map[string]interface{} // 用户属性
    TenantID    string                 // 租户ID（多租户支持）
}
```

### 权限策略

支持多种权限策略引擎：

#### 1. 内置策略引擎
- **路径匹配**: 支持精确匹配、通配符匹配、正则表达式匹配
- **方法过滤**: 支持 HTTP 方法过滤
- **角色检查**: 基于用户角色的访问控制
- **权限检查**: 基于用户权限的访问控制
- **条件评估**: 支持时间、IP、用户属性等条件

#### 2. OPA (Open Policy Agent) 集成
- **Rego 语言**: 支持 OPA 的 Rego 策略语言
- **动态策略**: 支持动态加载和更新策略
- **复杂逻辑**: 支持复杂的授权逻辑

**策略配置示例**:
```yaml
auth:
  policies:
    engine: "builtin"
    policies:
      - name: "admin_access"
        path: "/admin/*"
        method: "*"
        roles: ["admin"]
        permissions: ["admin:read", "admin:write"]
        conditions:
          time_range: "08:00-18:00"
          ip_range: "***********/24"
          user_attribute: "department=engineering"
```

## 令牌管理

### 令牌类型
- **访问令牌** (Access Token): 用于 API 访问
- **刷新令牌** (Refresh Token): 用于获取新的访问令牌
- **ID 令牌** (ID Token): 包含用户身份信息

### 令牌功能
- **存储管理**: 支持内存和持久化存储
- **验证机制**: 完整的令牌验证流程
- **刷新机制**: 自动令牌刷新
- **撤销机制**: 支持单个令牌和批量撤销
- **黑名单**: 令牌黑名单机制
- **自动清理**: 过期令牌自动清理

### 令牌信息
```go
type TokenInfo struct {
    ID           string                 // 令牌唯一标识
    Token        string                 // 令牌值
    Type         TokenType              // 令牌类型
    UserID       string                 // 用户ID
    ProviderID   string                 // ID 提供者ID
    ExpiresAt    time.Time              // 过期时间
    Roles        []string               // 角色列表
    Permissions  []string               // 权限列表
    Attributes   map[string]interface{} // 用户属性
}
```

## 管理 API

### ID Provider 管理
```http
# 列出所有 ID 提供者
GET /admin/auth/providers

# 创建 ID 提供者
POST /admin/auth/providers

# 获取 ID 提供者详情
GET /admin/auth/providers/{id}

# 更新 ID 提供者
PUT /admin/auth/providers/{id}

# 删除 ID 提供者
DELETE /admin/auth/providers/{id}

# 启用/禁用 ID 提供者
POST /admin/auth/providers/{id}/enable
POST /admin/auth/providers/{id}/disable

# 测试 ID 提供者连接
POST /admin/auth/providers/{id}/test
```

### 令牌管理
```http
# 列出令牌
GET /admin/auth/tokens

# 获取令牌详情
GET /admin/auth/tokens/{id}

# 撤销令牌
DELETE /admin/auth/tokens/{id}

# 刷新令牌
POST /admin/auth/tokens/{id}/refresh

# 撤销用户所有令牌
DELETE /admin/auth/tokens/user/{user_id}

# 获取令牌统计
GET /admin/auth/tokens/stats
```

### 权限管理
```http
# 权限管理
GET /admin/auth/permissions
POST /admin/auth/permissions
GET /admin/auth/permissions/{id}
PUT /admin/auth/permissions/{id}
DELETE /admin/auth/permissions/{id}

# 角色管理
GET /admin/auth/roles
POST /admin/auth/roles
GET /admin/auth/roles/{id}
PUT /admin/auth/roles/{id}
DELETE /admin/auth/roles/{id}

# 用户管理
GET /admin/auth/users
POST /admin/auth/users
GET /admin/auth/users/{id}
PUT /admin/auth/users/{id}
DELETE /admin/auth/users/{id}
```

## 多租户支持

### 租户隔离
- **数据隔离**: 每个租户的数据完全隔离
- **权限隔离**: 租户间权限不互相影响
- **配置隔离**: 每个租户可以有独立的配置

### 租户管理
```go
type Tenant struct {
    ID          string                 // 租户唯一标识
    Name        string                 // 租户名称
    Description string                 // 租户描述
    Status      string                 // 租户状态
    Settings    map[string]interface{} // 租户设置
}
```

## 性能优化

### 权限缓存
- **内存缓存**: 高性能的内存权限缓存
- **TTL 控制**: 可配置的缓存过期时间
- **自动失效**: 权限变更时自动失效相关缓存

### 连接池
- **数据库连接池**: 优化数据库连接使用
- **HTTP 连接池**: 优化外部服务调用

### 异步处理
- **异步日志**: 异步记录认证和授权日志
- **后台清理**: 后台定期清理过期数据

## 安全特性

### 安全加固
- **密钥管理**: 安全的密钥存储和轮换
- **传输加密**: 所有通信使用 TLS 加密
- **敏感数据**: 敏感数据脱敏和加密存储

### 审计日志
- **完整记录**: 记录所有认证和授权操作
- **结构化日志**: 使用结构化格式便于分析
- **日志轮转**: 自动日志轮转和归档

### 攻击防护
- **暴力破解**: 防止暴力破解攻击
- **重放攻击**: 防止令牌重放攻击
- **会话固定**: 防止会话固定攻击

## 监控和告警

### 指标监控
- **认证成功率**: 监控认证成功和失败率
- **权限检查**: 监控权限检查性能
- **令牌使用**: 监控令牌使用情况

### 告警规则
- **认证失败**: 认证失败率过高告警
- **权限拒绝**: 权限拒绝率异常告警
- **系统异常**: 系统异常和错误告警

## 部署和配置

### 环境要求
- **Go 版本**: Go 1.19+
- **内存要求**: 最少 512MB
- **存储要求**: 根据令牌和用户数量确定

### 配置文件
参考 `configs/gateway-idprovider-example.yaml` 获取完整的配置示例。

### 启动参数
```bash
./api-gateway -config configs/gateway-idprovider-example.yaml
```

## 最佳实践

### 安全配置
1. **使用强密钥**: 使用足够长度的随机密钥
2. **定期轮换**: 定期轮换密钥和证书
3. **最小权限**: 遵循最小权限原则
4. **网络隔离**: 在安全的网络环境中部署

### 性能优化
1. **缓存配置**: 合理配置权限缓存
2. **连接池**: 优化数据库和HTTP连接池
3. **监控调优**: 根据监控数据进行性能调优

### 运维管理
1. **日志管理**: 配置合适的日志级别和轮转
2. **备份策略**: 定期备份配置和数据
3. **升级策略**: 制定安全的升级策略

## 故障排除

### 常见问题
1. **认证失败**: 检查 ID Provider 配置和网络连接
2. **权限拒绝**: 检查用户角色和权限配置
3. **令牌过期**: 检查令牌过期时间和刷新机制

### 调试工具
1. **日志分析**: 使用结构化日志进行问题分析
2. **API 测试**: 使用管理 API 进行配置验证
3. **监控指标**: 使用监控指标定位性能问题
