# ID Provider 权限控制系统部署指南

## 概述

本指南详细介绍了如何部署和配置 API 网关的 ID Provider 权限控制系统。该系统支持多种部署方式，包括单机部署、集群部署和容器化部署。

## 系统要求

### 硬件要求

**最低配置**
- CPU: 2 核心
- 内存: 4GB RAM
- 存储: 20GB 可用空间
- 网络: 100Mbps

**推荐配置**
- CPU: 4 核心或更多
- 内存: 8GB RAM 或更多
- 存储: 100GB SSD
- 网络: 1Gbps

**生产环境配置**
- CPU: 8 核心或更多
- 内存: 16GB RAM 或更多
- 存储: 500GB SSD
- 网络: 10Gbps
- 负载均衡器
- 高可用存储

### 软件要求

- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+, RHEL 8+)
- **Go 版本**: Go 1.19 或更高版本
- **数据库**: PostgreSQL 12+ 或 MySQL 8.0+ (可选)
- **缓存**: Redis 6.0+ (可选)
- **负载均衡**: Nginx, HAProxy 或云负载均衡器
- **监控**: Prometheus + Grafana (推荐)

## 安装步骤

### 1. 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要的工具
sudo apt install -y curl wget git build-essential

# 安装 Go (如果未安装)
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
source ~/.bashrc
```

### 2. 下载和编译

```bash
# 克隆代码库
git clone https://github.com/your-org/api-gateway.git
cd api-gateway

# 安装依赖
go mod download

# 编译项目
go build -o api-gateway cmd/gateway/main.go

# 验证编译结果
./api-gateway --version
```

### 3. 配置文件准备

```bash
# 创建配置目录
sudo mkdir -p /etc/api-gateway
sudo mkdir -p /var/log/api-gateway
sudo mkdir -p /var/lib/api-gateway

# 复制配置文件
sudo cp configs/gateway-idprovider-example.yaml /etc/api-gateway/config.yaml

# 设置权限
sudo chown -R api-gateway:api-gateway /etc/api-gateway
sudo chown -R api-gateway:api-gateway /var/log/api-gateway
sudo chown -R api-gateway:api-gateway /var/lib/api-gateway
```

### 4. 配置文件修改

编辑 `/etc/api-gateway/config.yaml`：

```yaml
# 服务器配置
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30

# 认证配置
auth:
  jwt:
    enabled: true
    secret: "your-production-secret-key-change-this"
    algorithm: "HS256"
    expiration: 3600

  oidc:
    enabled: true
    issuer: "https://your-oidc-provider.com"
    client_id: "your-client-id"
    client_secret: "your-client-secret"

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "file"
  file: "/var/log/api-gateway/gateway.log"

# 监控配置
metrics:
  enabled: true
  path: "/metrics"
  port: 9090
```

### 5. 创建系统服务

创建 systemd 服务文件 `/etc/systemd/system/api-gateway.service`：

```ini
[Unit]
Description=API Gateway with ID Provider
After=network.target

[Service]
Type=simple
User=api-gateway
Group=api-gateway
WorkingDirectory=/opt/api-gateway
ExecStart=/opt/api-gateway/api-gateway -config /etc/api-gateway/config.yaml
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=api-gateway

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/api-gateway /var/lib/api-gateway

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

### 6. 启动服务

```bash
# 重新加载 systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable api-gateway

# 启动服务
sudo systemctl start api-gateway

# 检查状态
sudo systemctl status api-gateway

# 查看日志
sudo journalctl -u api-gateway -f
```

## 配置详解

### 认证配置

#### JWT 配置
```yaml
auth:
  jwt:
    enabled: true
    secret: "your-secret-key"  # 生产环境请使用强密钥
    algorithm: "HS256"         # 支持的算法
    expiration: 3600           # Token 过期时间（秒）
```

#### OIDC 配置
```yaml
auth:
  oidc:
    enabled: true
    issuer: "https://your-oidc-provider.com"
    client_id: "your-client-id"
    client_secret: "your-client-secret"
    redirect_url: "https://your-gateway.com/auth/callback"
    scopes: ["openid", "profile", "email", "roles"]
    skip_verify: false  # 生产环境设置为 false
```

#### mTLS 配置
```yaml
auth:
  mtls:
    enabled: true
    ca_file: "/etc/api-gateway/certs/ca.crt"
    crl_file: "/etc/api-gateway/certs/ca.crl"
    ocsp_enabled: true
    verify_client_cert_cn: true
    allowed_cns: ["client1.example.com"]
```

### 安全配置

#### TLS 配置
```yaml
server:
  tls:
    enabled: true
    cert_file: "/etc/api-gateway/certs/server.crt"
    key_file: "/etc/api-gateway/certs/server.key"
    mtls_mode: "request"  # none, request, require
```

#### 限流配置
```yaml
security:
  rate_limit:
    enabled: true
    rules:
      - path: "/api/*"
        method: "*"
        rate: 100
        burst: 20
        window: 60s
        key_by: "user"
```

## 高可用部署

### 负载均衡配置

#### Nginx 配置示例
```nginx
upstream api_gateway {
    server *********:8080 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8080 weight=1 max_fails=3 fail_timeout=30s;
    server *********:8080 weight=1 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name api.example.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/api.example.com.crt;
    ssl_certificate_key /etc/nginx/ssl/api.example.com.key;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 健康检查
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
    }

    # 健康检查端点
    location /health {
        proxy_pass http://api_gateway/health;
        access_log off;
    }

    # 监控端点
    location /metrics {
        proxy_pass http://api_gateway:9090/metrics;
        allow 10.0.0.0/8;
        deny all;
    }
}
```

### 数据库配置

#### PostgreSQL 配置
```yaml
database:
  type: "postgres"
  host: "postgres-cluster.example.com"
  port: 5432
  database: "api_gateway"
  username: "gateway_user"
  password: "secure_password"
  ssl_mode: "require"
  max_connections: 100
  max_idle_connections: 10
  connection_lifetime: "1h"
```

#### Redis 配置
```yaml
cache:
  enabled: true
  type: "redis"
  redis:
    addresses: ["redis-1:6379", "redis-2:6379", "redis-3:6379"]
    password: "redis_password"
    database: 0
    pool_size: 100
    min_idle_connections: 10
    max_retries: 3
    retry_delay: "100ms"
```

## 容器化部署

### Docker 部署

#### Dockerfile
```dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o api-gateway cmd/gateway/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/api-gateway .
COPY --from=builder /app/configs/gateway-idprovider-example.yaml ./config.yaml

EXPOSE 8080 9090
CMD ["./api-gateway", "-config", "./config.yaml"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  api-gateway:
    build: .
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - CONFIG_FILE=/app/config.yaml
    volumes:
      - ./configs/production.yaml:/app/config.yaml:ro
      - ./certs:/app/certs:ro
      - logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: api_gateway
      POSTGRES_USER: gateway_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass redis_password
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./certs:/etc/nginx/ssl:ro
    depends_on:
      - api-gateway
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  logs:
```

### Kubernetes 部署

#### Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  labels:
    app: api-gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: your-registry/api-gateway:latest
        ports:
        - containerPort: 8080
        - containerPort: 9090
        env:
        - name: CONFIG_FILE
          value: "/app/config.yaml"
        volumeMounts:
        - name: config
          mountPath: /app/config.yaml
          subPath: config.yaml
        - name: certs
          mountPath: /app/certs
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: api-gateway-config
      - name: certs
        secret:
          secretName: api-gateway-certs
```

#### Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: api-gateway-service
spec:
  selector:
    app: api-gateway
  ports:
  - name: http
    port: 80
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: LoadBalancer
```

## 监控和日志

### Prometheus 配置
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'api-gateway'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 10s
```

### Grafana 仪表板

导入预配置的仪表板来监控：
- 请求量和响应时间
- 认证成功率和失败率
- 权限检查性能
- 令牌使用统计
- 系统资源使用情况

### 日志聚合

#### ELK Stack 配置
```yaml
# Filebeat 配置
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/api-gateway/*.log
  json.keys_under_root: true
  json.add_error_key: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]
  index: "api-gateway-%{+yyyy.MM.dd}"
```

## 安全加固

### 系统安全
1. **防火墙配置**: 只开放必要端口
2. **用户权限**: 使用专用用户运行服务
3. **文件权限**: 限制配置文件访问权限
4. **定期更新**: 保持系统和依赖更新

### 应用安全
1. **密钥管理**: 使用环境变量或密钥管理服务
2. **TLS 配置**: 启用 HTTPS 和强加密套件
3. **访问控制**: 配置适当的 RBAC 策略
4. **审计日志**: 启用详细的审计日志

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查配置文件语法
   - 验证端口是否被占用
   - 查看系统日志

2. **认证失败**
   - 验证 ID Provider 配置
   - 检查网络连接
   - 确认证书有效性

3. **性能问题**
   - 检查资源使用情况
   - 优化数据库连接
   - 调整缓存配置

### 调试命令
```bash
# 检查服务状态
sudo systemctl status api-gateway

# 查看实时日志
sudo journalctl -u api-gateway -f

# 检查配置
./api-gateway -config /etc/api-gateway/config.yaml -validate

# 测试连接
curl -k https://localhost:8080/health

# 检查监控指标
curl http://localhost:9090/metrics
```

## 备份和恢复

### 配置备份
```bash
# 备份配置文件
sudo tar -czf api-gateway-config-$(date +%Y%m%d).tar.gz /etc/api-gateway

# 备份证书
sudo tar -czf api-gateway-certs-$(date +%Y%m%d).tar.gz /etc/api-gateway/certs
```

### 数据备份
```bash
# PostgreSQL 备份
pg_dump -h localhost -U gateway_user api_gateway > backup.sql

# Redis 备份
redis-cli --rdb backup.rdb
```

## 升级指南

### 滚动升级步骤
1. 备份当前配置和数据
2. 更新一个实例
3. 验证功能正常
4. 逐步更新其他实例
5. 验证整体系统功能

### 版本兼容性
- 检查配置文件格式变更
- 验证 API 兼容性
- 测试认证流程
- 确认监控指标正常
