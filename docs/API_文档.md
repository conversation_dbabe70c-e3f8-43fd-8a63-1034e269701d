# ID Provider 权限控制系统 API 文档

## 概述

本文档描述了 API 网关中 ID Provider 权限控制系统的管理 API 接口。所有 API 都遵循 RESTful 设计原则，使用 JSON 格式进行数据交换。

## 基础信息

- **基础 URL**: `/admin/auth`
- **认证方式**: Bearer Token 或 API Key
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

## ID Provider 管理 API

### 1. 列出所有 ID 提供者

**请求**
```http
GET /admin/auth/providers
```

**响应**
```json
{
  "providers": [
    {
      "id": "jwt-provider-1",
      "name": "JWT 提供者",
      "type": "jwt",
      "description": "JWT 认证提供者",
      "status": "active",
      "priority": 10,
      "enabled": true,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. 创建 ID 提供者

**请求**
```http
POST /admin/auth/providers
Content-Type: application/json

{
  "id": "new-provider",
  "name": "新的提供者",
  "type": "oidc",
  "description": "OIDC 认证提供者",
  "priority": 20,
  "enabled": true,
  "config": {
    "issuer": "https://example.com",
    "client_id": "client-id",
    "client_secret": "client-secret"
  }
}
```

**响应**
```json
{
  "provider": {
    "id": "new-provider",
    "name": "新的提供者",
    "type": "oidc",
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 获取 ID 提供者详情

**请求**
```http
GET /admin/auth/providers/{id}
```

**响应**
```json
{
  "provider": {
    "id": "jwt-provider-1",
    "name": "JWT 提供者",
    "type": "jwt",
    "description": "JWT 认证提供者",
    "status": "active",
    "priority": 10,
    "enabled": true,
    "config": {
      "algorithm": "HS256",
      "expiration": 3600
    },
    "metadata": {
      "source": "config"
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

### 4. 更新 ID 提供者

**请求**
```http
PUT /admin/auth/providers/{id}
Content-Type: application/json

{
  "name": "更新的提供者名称",
  "description": "更新的描述",
  "priority": 15,
  "enabled": false,
  "config": {
    "algorithm": "HS512",
    "expiration": 7200
  }
}
```

### 5. 删除 ID 提供者

**请求**
```http
DELETE /admin/auth/providers/{id}
```

**响应**
```http
HTTP/1.1 204 No Content
```

### 6. 启用/禁用 ID 提供者

**启用提供者**
```http
POST /admin/auth/providers/{id}/enable
```

**禁用提供者**
```http
POST /admin/auth/providers/{id}/disable
```

### 7. 测试 ID 提供者连接

**请求**
```http
POST /admin/auth/providers/{id}/test
```

**响应**
```json
{
  "message": "连接测试成功"
}
```

### 8. 获取支持的提供者类型

**请求**
```http
GET /admin/auth/providers/types
```

**响应**
```json
{
  "types": ["jwt", "oidc", "oauth2", "saml", "ldap", "apikey", "mtls"]
}
```

### 9. 获取提供者配置模式

**请求**
```http
GET /admin/auth/providers/types/{type}/schema
```

**响应**
```json
{
  "schema": {
    "type": "object",
    "properties": {
      "issuer": {
        "type": "string",
        "description": "OIDC 提供者的 issuer URL"
      },
      "client_id": {
        "type": "string",
        "description": "OIDC 客户端 ID"
      }
    },
    "required": ["issuer", "client_id"]
  }
}
```

## 令牌管理 API

### 1. 列出令牌

**请求**
```http
GET /admin/auth/tokens?user_id=user123&page=1&limit=20
```

**查询参数**
- `user_id`: 用户ID过滤
- `provider_id`: 提供者ID过滤
- `status`: 状态过滤 (active, revoked, expired)
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)

**响应**
```json
{
  "tokens": [
    {
      "id": "token-123",
      "type": "access",
      "user_id": "user123",
      "provider_id": "jwt-provider-1",
      "status": "active",
      "expires_at": "2024-01-01T01:00:00Z",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 20
}
```

### 2. 获取令牌详情

**请求**
```http
GET /admin/auth/tokens/{id}
```

**响应**
```json
{
  "token": {
    "id": "token-123",
    "type": "access",
    "user_id": "user123",
    "username": "testuser",
    "provider_id": "jwt-provider-1",
    "provider_type": "jwt",
    "status": "active",
    "roles": ["user", "admin"],
    "permissions": ["read", "write"],
    "scopes": ["api:read", "api:write"],
    "expires_at": "2024-01-01T01:00:00Z",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 撤销令牌

**请求**
```http
DELETE /admin/auth/tokens/{id}
```

**响应**
```json
{
  "message": "令牌已撤销"
}
```

### 4. 刷新令牌

**请求**
```http
POST /admin/auth/tokens/{id}/refresh
```

**响应**
```json
{
  "token": {
    "id": "new-token-456",
    "type": "access",
    "user_id": "user123",
    "token": "new-access-token",
    "expires_at": "2024-01-01T02:00:00Z"
  }
}
```

### 5. 撤销用户所有令牌

**请求**
```http
DELETE /admin/auth/tokens/user/{user_id}
```

### 6. 撤销提供者所有令牌

**请求**
```http
DELETE /admin/auth/tokens/provider/{provider_id}
```

### 7. 获取令牌统计

**请求**
```http
GET /admin/auth/tokens/stats
```

**响应**
```json
{
  "stats": {
    "total_tokens": 1000,
    "active_tokens": 800,
    "expired_tokens": 150,
    "revoked_tokens": 50,
    "blacklist_size": 25,
    "tokens_by_type": {
      "access": 800,
      "refresh": 200
    },
    "tokens_by_provider": {
      "jwt-provider-1": 600,
      "oidc-provider-1": 400
    }
  }
}
```

### 8. 清理过期令牌

**请求**
```http
POST /admin/auth/tokens/cleanup
```

## 权限管理 API

### 1. 列出权限

**请求**
```http
GET /admin/auth/permissions?tenant_id=tenant1
```

**响应**
```json
{
  "permissions": [
    {
      "id": "read-users",
      "name": "读取用户",
      "description": "读取用户信息的权限",
      "resource": "users",
      "action": "read",
      "conditions": {
        "time_range": "08:00-18:00"
      },
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. 创建权限

**请求**
```http
POST /admin/auth/permissions
Content-Type: application/json

{
  "id": "write-orders",
  "name": "写入订单",
  "description": "创建和修改订单的权限",
  "resource": "orders",
  "action": "write",
  "conditions": {
    "ip_range": "***********/24"
  }
}
```

### 3. 获取权限详情

**请求**
```http
GET /admin/auth/permissions/{id}
```

### 4. 更新权限

**请求**
```http
PUT /admin/auth/permissions/{id}
```

### 5. 删除权限

**请求**
```http
DELETE /admin/auth/permissions/{id}
```

## 角色管理 API

### 1. 列出角色

**请求**
```http
GET /admin/auth/roles?tenant_id=tenant1
```

**响应**
```json
{
  "roles": [
    {
      "id": "admin",
      "name": "管理员",
      "description": "系统管理员角色",
      "permissions": ["read-users", "write-users", "read-orders"],
      "parent_roles": ["user"],
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. 创建角色

**请求**
```http
POST /admin/auth/roles
Content-Type: application/json

{
  "id": "editor",
  "name": "编辑者",
  "description": "内容编辑者角色",
  "permissions": ["read-content", "write-content"],
  "parent_roles": ["user"]
}
```

### 3. 为角色分配权限

**请求**
```http
POST /admin/auth/roles/{role_id}/permissions/{permission_id}
```

### 4. 从角色移除权限

**请求**
```http
DELETE /admin/auth/roles/{role_id}/permissions/{permission_id}
```

## 用户管理 API

### 1. 列出用户

**请求**
```http
GET /admin/auth/users?tenant_id=tenant1
```

### 2. 创建用户

**请求**
```http
POST /admin/auth/users
Content-Type: application/json

{
  "id": "user123",
  "username": "testuser",
  "email": "<EMAIL>",
  "display_name": "测试用户",
  "roles": ["user"],
  "tenant_id": "tenant1",
  "status": "active"
}
```

### 3. 为用户分配角色

**请求**
```http
POST /admin/auth/users/{user_id}/roles/{role_id}
```

### 4. 获取用户权限

**请求**
```http
GET /admin/auth/users/{user_id}/permissions
```

### 5. 获取用户令牌

**请求**
```http
GET /admin/auth/users/{user_id}/tokens
```

## 租户管理 API

### 1. 列出租户

**请求**
```http
GET /admin/auth/tenants
```

### 2. 创建租户

**请求**
```http
POST /admin/auth/tenants
Content-Type: application/json

{
  "id": "tenant2",
  "name": "租户2",
  "description": "第二个租户",
  "status": "active",
  "settings": {
    "max_users": 1000,
    "features": ["sso", "audit"]
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| INVALID_REQUEST | 400 | 请求参数无效 |
| UNAUTHORIZED | 401 | 未授权访问 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| CONFLICT | 409 | 资源冲突 |
| VALIDATION_ERROR | 422 | 数据验证失败 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 使用示例

### 完整的用户权限管理流程

```bash
# 1. 创建权限
curl -X POST /admin/auth/permissions \
  -H "Content-Type: application/json" \
  -d '{
    "id": "read-api",
    "name": "API读取权限",
    "resource": "api",
    "action": "read"
  }'

# 2. 创建角色
curl -X POST /admin/auth/roles \
  -H "Content-Type: application/json" \
  -d '{
    "id": "api-user",
    "name": "API用户",
    "permissions": ["read-api"]
  }'

# 3. 创建用户
curl -X POST /admin/auth/users \
  -H "Content-Type: application/json" \
  -d '{
    "id": "user001",
    "username": "apiuser",
    "roles": ["api-user"]
  }'

# 4. 检查用户权限
curl -X GET /admin/auth/users/user001/permissions
```

## 认证和授权

所有管理 API 都需要适当的认证和授权：

1. **认证**: 使用 Bearer Token 或 API Key
2. **授权**: 需要 `admin:read` 和 `admin:write` 权限
3. **审计**: 所有操作都会记录审计日志

## 限流和配额

- **请求限制**: 每分钟最多 1000 次请求
- **并发限制**: 每个客户端最多 10 个并发连接
- **数据限制**: 单次请求最大 1MB 数据
