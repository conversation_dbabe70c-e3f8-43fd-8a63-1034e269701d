# API Gateway

A high-performance, enterprise-grade API Gateway with zero-trust security, built in Go.

## Features

### 🔐 Zero-Trust Security
- **Multi-factor Authentication**: JWT, API Keys, OAuth/OIDC, mTLS
- **Fine-grained Authorization**: Role-based and attribute-based access control
- **Policy Engine**: Built-in and OPA (Open Policy Agent) support
- **Web Application Firewall**: SQL injection, XSS, and other attack protection

### 🚦 Traffic Control & Protection
- **Advanced Rate Limiting**: Token bucket and leaky bucket algorithms
- **IP Filtering**: Whitelist/blacklist with CIDR support
- **CORS Management**: Flexible cross-origin resource sharing
- **Request/Response Transformation**: Header and body manipulation

### 🔄 Protocol Support & Resilience
- **Protocol Support**: HTTP/1.1, HTTP/2, WebSocket
- **Circuit Breaker**: Prevent cascade failures
- **Retry Logic**: Exponential backoff and jitter
- **Bulkhead Pattern**: Resource isolation

### 🎯 Service Discovery & Load Balancing
- **Service Discovery**: Consul, Nacos integration
- **Load Balancing**: Round-robin, weighted, IP hash, least connections
- **Health Checks**: Automatic upstream health monitoring
- **Dynamic Configuration**: Hot reload without downtime

### 📊 Observability
- **Comprehensive Metrics**: Prometheus-compatible metrics
- **Distributed Tracing**: Jaeger integration
- **Structured Logging**: JSON and text formats
- **Audit Logging**: Security event tracking
- **Health Checks**: Component and overall system health

### 🔌 Extensibility
- **Plugin System**: Custom middleware and transformations
- **Built-in Plugins**: Logging, metrics, caching, validation
- **Hot Plugin Loading**: Dynamic plugin management

### 🤖 MCP (Model Context Protocol) Support
- **无侵入式转换**: 将现有 REST API 自动转换为 MCP 服务格式
- **协议完整支持**: 基于 JSON-RPC 2.0 的完整 MCP 协议实现
- **灵活配置**: 支持路由级别的转换规则和参数映射
- **多种传输方式**: 支持 stdio、SSE、自定义传输协议

## Quick Start

### Prerequisites

- Go 1.21 or later
- Docker and Docker Compose (for development environment)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/api-gateway.git
   cd api-gateway
   ```

2. **Build the application**
   ```bash
   make build
   ```

3. **Run with default configuration**
   ```bash
   make run
   ```

### MCP 功能快速体验

体验无侵入式 API 到 MCP 转换功能：

```bash
# 1. 启动基础 MCP 示例
cd examples/mcp/basic
go run main.go

# 2. 测试普通 API
curl http://localhost:8080/api/v1/users

# 3. 测试 MCP 转换（添加转换头部）
curl -H "X-Convert-To-MCP: true" http://localhost:8080/api/v1/users

# 4. 直接发送 MCP 请求
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"users.list","params":{"pagination":{"page":1,"limit":10}}}'

# 5. 查看 MCP 统计信息
curl http://localhost:8080/mcp/stats

# 6. 体验 WebSocket 实时通信
cd ../websocket
go run main.go
# 然后访问 http://localhost:8080
```

### Development Environment

Start the complete development environment with all dependencies:

```bash
# Start all services (Consul, Redis, Jaeger, Prometheus, Grafana)
make services-up

# Run the gateway in development mode
make dev
```

Access the services:
- **API Gateway**: http://localhost:8080
- **Consul UI**: http://localhost:8500
- **Jaeger UI**: http://localhost:16686
- **Prometheus**: http://localhost:9091
- **Grafana**: http://localhost:3000 (admin/admin)

## Configuration

The gateway uses YAML configuration files. See `configs/gateway.yaml` for a complete example.

### MCP Configuration

The gateway supports MCP (Model Context Protocol) conversion. See `configs/mcp.yaml` for MCP-specific configuration:

```yaml
mcp:
  enabled: true
  protocol_version: "2024-11-05"

  conversion_rules:
    - path: "/api/v1/users"
      methods: ["GET", "POST"]
      mcp_method: "users.list"
      enabled: true
      parameter_mapping:
        query:
          page: "pagination.page"
          limit: "pagination.limit"
        body:
          name: "user.name"
          email: "user.email"

  server:
    name: "API Gateway MCP Server"
    version: "1.0.0"

  transport:
    type: "sse"
    sse:
      endpoint: "/mcp/sse"
      post_endpoint: "/mcp/message"
```

### Basic Configuration

```yaml
server:
  address: ":8080"
  read_timeout: 30
  write_timeout: 30

auth:
  jwt:
    enabled: true
    secret: "your-secret-key"
    algorithm: "HS256"

security:
  rate_limit:
    enabled: true
    rules:
      - path: "/api/*"
        rate: 100
        burst: 200

routes:
  - name: "user_service"
    path: "/api/v1/users/*"
    upstream:
      type: "static"
      servers:
        - host: "localhost"
          port: 8081
```

### Environment Variables

Key environment variables for configuration:

```bash
GATEWAY_CONFIG_FILE=/path/to/config.yaml
GATEWAY_LOG_LEVEL=info
GATEWAY_SERVER_PORT=8080
JWT_SECRET=your-secret-key
CONSUL_ADDRESS=localhost:8500
```

## API Documentation

### Health Check
```bash
curl http://localhost:8080/health
```

### Metrics
```bash
curl http://localhost:9090/metrics
```

### Authentication

#### JWT Token
```bash
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/users
```

#### API Key
```bash
curl -H "X-API-Key: <api-key>" \
     http://localhost:8080/api/v1/users
```

### MCP Protocol

#### MCP Message Endpoint
```bash
curl -X POST http://localhost:8080/mcp/message \
     -H "Content-Type: application/json" \
     -d '{
       "jsonrpc": "2.0",
       "id": 1,
       "method": "users.list",
       "params": {
         "pagination": {"page": 1, "limit": 10}
       }
     }'
```

#### MCP SSE Connection
```bash
curl -N http://localhost:8080/mcp/sse
```

#### HTTP to MCP Conversion
```bash
curl -H "X-Convert-To-MCP: true" \
     http://localhost:8080/api/v1/users?page=1&limit=10
```

## Deployment

### Docker

```bash
# Build Docker image
make docker-build

# Run with Docker
make docker-run
```

### Kubernetes

```bash
# Apply Kubernetes manifests
kubectl apply -f deployments/k8s/
```

### Production Deployment

1. **Configure TLS**
   ```yaml
   server:
     tls:
       enabled: true
       cert_file: "/path/to/cert.pem"
       key_file: "/path/to/key.pem"
   ```

2. **Set up service discovery**
   ```yaml
   discovery:
     type: "consul"
     consul:
       address: "consul.example.com:8500"
   ```

3. **Configure monitoring**
   ```yaml
   metrics:
     enabled: true
   tracing:
     enabled: true
     endpoint: "http://jaeger.example.com:14268/api/traces"
   ```

## Development

### Building

```bash
# Build for current platform
make build

# Build for all platforms
make release

# Build Docker image
make docker-build
```

### Testing

```bash
# Run tests
make test

# Run tests with coverage
make test-coverage

# Run benchmarks
make bench

# Security scan
make security-scan
```

### Code Quality

```bash
# Format code
make fmt

# Run linter
make lint

# Run go vet
make vet
```

## Monitoring

### Metrics

The gateway exposes Prometheus-compatible metrics:

- Request count and duration
- Error rates by status code
- Active connections
- Rate limit hits
- Circuit breaker states
- Plugin execution times

### Logging

Structured logging with configurable levels and formats:

```json
{
  "timestamp": "2023-01-01T00:00:00Z",
  "level": "info",
  "message": "Request processed",
  "request_id": "req-123",
  "method": "GET",
  "path": "/api/v1/users",
  "status": 200,
  "duration": "15ms"
}
```

### Tracing

Distributed tracing with Jaeger integration for request flow visualization.

## Security

### Authentication Methods

1. **JWT Tokens**: Stateless authentication with configurable algorithms
2. **API Keys**: Simple key-based authentication
3. **OAuth/OIDC**: Integration with identity providers
4. **mTLS**: Certificate-based authentication

### Security Features

- Rate limiting with multiple algorithms
- Web Application Firewall (WAF)
- IP filtering and geoblocking
- CORS protection
- Security headers injection
- Audit logging

## Performance

### Benchmarks

```bash
# Performance test
make perf-test

# Load test
make load-test
```

### Optimization Tips

1. **Enable HTTP/2**: Better multiplexing and performance
2. **Configure connection pooling**: Optimize upstream connections
3. **Use caching**: Reduce upstream load
4. **Tune rate limits**: Balance protection and performance

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

### Development Guidelines

- Follow Go best practices
- Write comprehensive tests
- Update documentation
- Use conventional commits

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: [Wiki](https://github.com/your-org/api-gateway/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/api-gateway/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/api-gateway/discussions)

## Roadmap

- [x] **MCP (Model Context Protocol) Support**: 无侵入式 API 到 MCP 转换功能
- [ ] GraphQL support
- [ ] gRPC protocol support
- [ ] Advanced caching strategies
- [ ] Machine learning-based anomaly detection
- [ ] Multi-region deployment support
- [ ] Advanced policy languages
- [ ] Enhanced MCP features (WebSocket transport, advanced parameter mapping)