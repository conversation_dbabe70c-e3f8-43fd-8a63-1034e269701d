package auth

import (
	"fmt"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// JWTAuthenticatorFactory JWT 认证器工厂
type JWTAuthenticatorFactory struct{}

// CreateAuthenticator 创建 JWT 认证器
func (f *JWTAuthenticatorFactory) CreateAuthenticator(providerConfig *IDProviderConfig, logger *telemetry.Logger) (Authenticator, error) {
	// 将通用配置转换为 JWT 配置
	jwtConfig, err := f.convertToJWTConfig(providerConfig.Config)
	if err != nil {
		return nil, fmt.Errorf("转换 JWT 配置失败: %w", err)
	}
	
	return NewJWTAuthenticator(*jwtConfig, logger)
}

// ValidateConfig 验证 JWT 配置
func (f *JWTAuthenticatorFactory) ValidateConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.<PERSON><PERSON><PERSON>("JWT 配置不能为空")
	}
	
	// 验证必需字段
	if _, exists := config["secret"]; !exists {
		if _, exists := config["public_key"]; !exists {
			return fmt.Errorf("JWT 配置必须包含 secret 或 public_key")
		}
	}
	
	// 验证算法
	if algorithm, exists := config["algorithm"]; exists {
		if alg, ok := algorithm.(string); ok {
			validAlgorithms := []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "ES256", "ES384", "ES512"}
			valid := false
			for _, validAlg := range validAlgorithms {
				if alg == validAlg {
					valid = true
					break
				}
			}
			if !valid {
				return fmt.Errorf("不支持的 JWT 算法: %s", alg)
			}
		}
	}
	
	return nil
}

// GetConfigSchema 获取 JWT 配置模式
func (f *JWTAuthenticatorFactory) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"secret": map[string]interface{}{
				"type":        "string",
				"description": "JWT 签名密钥（用于 HMAC 算法）",
			},
			"public_key": map[string]interface{}{
				"type":        "string",
				"description": "JWT 公钥（用于 RSA/ECDSA 算法）",
			},
			"algorithm": map[string]interface{}{
				"type":        "string",
				"description": "JWT 签名算法",
				"enum":        []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "ES256", "ES384", "ES512"},
				"default":     "HS256",
			},
			"expiration": map[string]interface{}{
				"type":        "integer",
				"description": "Token 过期时间（秒）",
				"default":     3600,
			},
		},
		"required": []string{},
		"oneOf": []map[string]interface{}{
			{"required": []string{"secret"}},
			{"required": []string{"public_key"}},
		},
	}
}

// convertToJWTConfig 转换为 JWT 配置
func (f *JWTAuthenticatorFactory) convertToJWTConfig(config map[string]interface{}) (*config.JWTConfig, error) {
	jwtConfig := &config.JWTConfig{
		Enabled: true,
	}
	
	if secret, exists := config["secret"]; exists {
		if s, ok := secret.(string); ok {
			jwtConfig.Secret = s
		}
	}
	
	if publicKey, exists := config["public_key"]; exists {
		if pk, ok := publicKey.(string); ok {
			jwtConfig.PublicKey = pk
		}
	}
	
	if algorithm, exists := config["algorithm"]; exists {
		if alg, ok := algorithm.(string); ok {
			jwtConfig.Algorithm = alg
		} else {
			jwtConfig.Algorithm = "HS256"
		}
	} else {
		jwtConfig.Algorithm = "HS256"
	}
	
	if expiration, exists := config["expiration"]; exists {
		if exp, ok := expiration.(int); ok {
			jwtConfig.Expiration = exp
		} else if exp, ok := expiration.(float64); ok {
			jwtConfig.Expiration = int(exp)
		} else {
			jwtConfig.Expiration = 3600
		}
	} else {
		jwtConfig.Expiration = 3600
	}
	
	return jwtConfig, nil
}

// OIDCAuthenticatorFactory OIDC 认证器工厂
type OIDCAuthenticatorFactory struct{}

// CreateAuthenticator 创建 OIDC 认证器
func (f *OIDCAuthenticatorFactory) CreateAuthenticator(providerConfig *IDProviderConfig, logger *telemetry.Logger) (Authenticator, error) {
	// 将通用配置转换为 OIDC 配置
	oidcConfig, err := f.convertToOIDCConfig(providerConfig.Config)
	if err != nil {
		return nil, fmt.Errorf("转换 OIDC 配置失败: %w", err)
	}
	
	return NewOIDCAuthenticator(*oidcConfig, logger)
}

// ValidateConfig 验证 OIDC 配置
func (f *OIDCAuthenticatorFactory) ValidateConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("OIDC 配置不能为空")
	}
	
	// 验证必需字段
	requiredFields := []string{"issuer", "client_id", "client_secret"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("OIDC 配置缺少必需字段: %s", field)
		}
	}
	
	// 验证 issuer URL
	if issuer, exists := config["issuer"]; exists {
		if issuerStr, ok := issuer.(string); ok {
			if issuerStr == "" {
				return fmt.Errorf("OIDC issuer 不能为空")
			}
		} else {
			return fmt.Errorf("OIDC issuer 必须是字符串")
		}
	}
	
	return nil
}

// GetConfigSchema 获取 OIDC 配置模式
func (f *OIDCAuthenticatorFactory) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"issuer": map[string]interface{}{
				"type":        "string",
				"description": "OIDC 提供者的 issuer URL",
				"format":      "uri",
			},
			"client_id": map[string]interface{}{
				"type":        "string",
				"description": "OIDC 客户端 ID",
			},
			"client_secret": map[string]interface{}{
				"type":        "string",
				"description": "OIDC 客户端密钥",
			},
			"redirect_url": map[string]interface{}{
				"type":        "string",
				"description": "重定向 URL",
				"format":      "uri",
			},
			"scopes": map[string]interface{}{
				"type":        "array",
				"description": "请求的权限范围",
				"items": map[string]interface{}{
					"type": "string",
				},
				"default": []string{"openid", "profile", "email"},
			},
			"skip_verify": map[string]interface{}{
				"type":        "boolean",
				"description": "是否跳过 TLS 证书验证",
				"default":     false,
			},
		},
		"required": []string{"issuer", "client_id", "client_secret"},
	}
}

// convertToOIDCConfig 转换为 OIDC 配置
func (f *OIDCAuthenticatorFactory) convertToOIDCConfig(config map[string]interface{}) (*config.OIDCConfig, error) {
	oidcConfig := &config.OIDCConfig{
		Enabled: true,
	}
	
	if issuer, exists := config["issuer"]; exists {
		if s, ok := issuer.(string); ok {
			oidcConfig.Issuer = s
		}
	}
	
	if clientID, exists := config["client_id"]; exists {
		if s, ok := clientID.(string); ok {
			oidcConfig.ClientID = s
		}
	}
	
	if clientSecret, exists := config["client_secret"]; exists {
		if s, ok := clientSecret.(string); ok {
			oidcConfig.ClientSecret = s
		}
	}
	
	if redirectURL, exists := config["redirect_url"]; exists {
		if s, ok := redirectURL.(string); ok {
			oidcConfig.RedirectURL = s
		}
	}
	
	if scopes, exists := config["scopes"]; exists {
		if scopeSlice, ok := scopes.([]interface{}); ok {
			oidcConfig.Scopes = make([]string, len(scopeSlice))
			for i, scope := range scopeSlice {
				if s, ok := scope.(string); ok {
					oidcConfig.Scopes[i] = s
				}
			}
		} else if scopeSlice, ok := scopes.([]string); ok {
			oidcConfig.Scopes = scopeSlice
		}
	}
	
	if skipVerify, exists := config["skip_verify"]; exists {
		if b, ok := skipVerify.(bool); ok {
			oidcConfig.SkipVerify = b
		}
	}
	
	return oidcConfig, nil
}

// APIKeyAuthenticatorFactory API Key 认证器工厂
type APIKeyAuthenticatorFactory struct{}

// CreateAuthenticator 创建 API Key 认证器
func (f *APIKeyAuthenticatorFactory) CreateAuthenticator(providerConfig *IDProviderConfig, logger *telemetry.Logger) (Authenticator, error) {
	// 将通用配置转换为 API Key 配置
	apiKeyConfig, err := f.convertToAPIKeyConfig(providerConfig.Config)
	if err != nil {
		return nil, fmt.Errorf("转换 API Key 配置失败: %w", err)
	}
	
	return NewAPIKeyAuthenticator(*apiKeyConfig, logger)
}

// ValidateConfig 验证 API Key 配置
func (f *APIKeyAuthenticatorFactory) ValidateConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("API Key 配置不能为空")
	}
	
	// 至少需要指定 header_name 或 query_param 之一
	hasHeader := false
	hasQuery := false
	
	if headerName, exists := config["header_name"]; exists {
		if h, ok := headerName.(string); ok && h != "" {
			hasHeader = true
		}
	}
	
	if queryParam, exists := config["query_param"]; exists {
		if q, ok := queryParam.(string); ok && q != "" {
			hasQuery = true
		}
	}
	
	if !hasHeader && !hasQuery {
		return fmt.Errorf("API Key 配置必须指定 header_name 或 query_param")
	}
	
	return nil
}

// GetConfigSchema 获取 API Key 配置模式
func (f *APIKeyAuthenticatorFactory) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"header_name": map[string]interface{}{
				"type":        "string",
				"description": "API Key 在 HTTP 头中的字段名",
				"default":     "X-API-Key",
			},
			"query_param": map[string]interface{}{
				"type":        "string",
				"description": "API Key 在查询参数中的字段名",
				"default":     "api_key",
			},
		},
		"anyOf": []map[string]interface{}{
			{"required": []string{"header_name"}},
			{"required": []string{"query_param"}},
		},
	}
}

// convertToAPIKeyConfig 转换为 API Key 配置
func (f *APIKeyAuthenticatorFactory) convertToAPIKeyConfig(config map[string]interface{}) (*config.APIKeyConfig, error) {
	apiKeyConfig := &config.APIKeyConfig{
		Enabled: true,
	}
	
	if headerName, exists := config["header_name"]; exists {
		if s, ok := headerName.(string); ok {
			apiKeyConfig.HeaderName = s
		}
	} else {
		apiKeyConfig.HeaderName = "X-API-Key"
	}
	
	if queryParam, exists := config["query_param"]; exists {
		if s, ok := queryParam.(string); ok {
			apiKeyConfig.QueryParam = s
		}
	} else {
		apiKeyConfig.QueryParam = "api_key"
	}
	
	return apiKeyConfig, nil
}

// MTLSAuthenticatorFactory mTLS 认证器工厂
type MTLSAuthenticatorFactory struct{}

// CreateAuthenticator 创建 mTLS 认证器
func (f *MTLSAuthenticatorFactory) CreateAuthenticator(providerConfig *IDProviderConfig, logger *telemetry.Logger) (Authenticator, error) {
	// 将通用配置转换为 mTLS 配置
	mtlsConfig, err := f.convertToMTLSConfig(providerConfig.Config)
	if err != nil {
		return nil, fmt.Errorf("转换 mTLS 配置失败: %w", err)
	}

	return NewMTLSAuthenticator(*mtlsConfig, logger)
}

// ValidateConfig 验证 mTLS 配置
func (f *MTLSAuthenticatorFactory) ValidateConfig(config map[string]interface{}) error {
	if config == nil {
		return fmt.Errorf("mTLS 配置不能为空")
	}

	// 验证必需字段
	if _, exists := config["ca_file"]; !exists {
		return fmt.Errorf("mTLS 配置缺少必需字段: ca_file")
	}

	// 验证 CA 文件路径
	if caFile, exists := config["ca_file"]; exists {
		if caFileStr, ok := caFile.(string); ok {
			if caFileStr == "" {
				return fmt.Errorf("mTLS ca_file 不能为空")
			}
		} else {
			return fmt.Errorf("mTLS ca_file 必须是字符串")
		}
	}

	return nil
}

// GetConfigSchema 获取 mTLS 配置模式
func (f *MTLSAuthenticatorFactory) GetConfigSchema() map[string]interface{} {
	return map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"ca_file": map[string]interface{}{
				"type":        "string",
				"description": "CA 证书文件路径",
			},
			"crl_file": map[string]interface{}{
				"type":        "string",
				"description": "CRL 文件路径（可选）",
			},
			"ocsp_enabled": map[string]interface{}{
				"type":        "boolean",
				"description": "是否启用 OCSP 检查",
				"default":     false,
			},
			"ocsp_timeout_seconds": map[string]interface{}{
				"type":        "integer",
				"description": "OCSP 超时时间（秒）",
				"default":     10,
			},
			"verify_client_cert_cn": map[string]interface{}{
				"type":        "boolean",
				"description": "是否验证客户端证书的 CN 字段",
				"default":     false,
			},
			"allowed_cns": map[string]interface{}{
				"type":        "array",
				"description": "允许的客户端证书 CN 列表",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
			"verify_organization": map[string]interface{}{
				"type":        "boolean",
				"description": "是否验证客户端证书的组织字段",
				"default":     false,
			},
			"allowed_organizations": map[string]interface{}{
				"type":        "array",
				"description": "允许的组织列表",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
		},
		"required": []string{"ca_file"},
	}
}

// convertToMTLSConfig 转换为 mTLS 配置
func (f *MTLSAuthenticatorFactory) convertToMTLSConfig(config map[string]interface{}) (*config.MTLSConfig, error) {
	mtlsConfig := &config.MTLSConfig{
		Enabled: true,
	}

	if caFile, exists := config["ca_file"]; exists {
		if s, ok := caFile.(string); ok {
			mtlsConfig.CAFile = s
		}
	}

	if crlFile, exists := config["crl_file"]; exists {
		if s, ok := crlFile.(string); ok {
			mtlsConfig.CRLFile = s
		}
	}

	if ocspEnabled, exists := config["ocsp_enabled"]; exists {
		if b, ok := ocspEnabled.(bool); ok {
			mtlsConfig.OCSPEnabled = b
		}
	}

	if ocspTimeout, exists := config["ocsp_timeout_seconds"]; exists {
		if timeout, ok := ocspTimeout.(int); ok {
			mtlsConfig.OCSPTimeoutSeconds = timeout
		} else if timeout, ok := ocspTimeout.(float64); ok {
			mtlsConfig.OCSPTimeoutSeconds = int(timeout)
		}
	}

	if verifyCN, exists := config["verify_client_cert_cn"]; exists {
		if b, ok := verifyCN.(bool); ok {
			mtlsConfig.VerifyClientCertCN = b
		}
	}

	if allowedCNs, exists := config["allowed_cns"]; exists {
		if cnSlice, ok := allowedCNs.([]interface{}); ok {
			mtlsConfig.AllowedCNs = make([]string, len(cnSlice))
			for i, cn := range cnSlice {
				if s, ok := cn.(string); ok {
					mtlsConfig.AllowedCNs[i] = s
				}
			}
		} else if cnSlice, ok := allowedCNs.([]string); ok {
			mtlsConfig.AllowedCNs = cnSlice
		}
	}

	if verifyOrg, exists := config["verify_organization"]; exists {
		if b, ok := verifyOrg.(bool); ok {
			mtlsConfig.VerifyOrganization = b
		}
	}

	if allowedOrgs, exists := config["allowed_organizations"]; exists {
		if orgSlice, ok := allowedOrgs.([]interface{}); ok {
			mtlsConfig.AllowedOrganizations = make([]string, len(orgSlice))
			for i, org := range orgSlice {
				if s, ok := org.(string); ok {
					mtlsConfig.AllowedOrganizations[i] = s
				}
			}
		} else if orgSlice, ok := allowedOrgs.([]string); ok {
			mtlsConfig.AllowedOrganizations = orgSlice
		}
	}

	return mtlsConfig, nil
}
