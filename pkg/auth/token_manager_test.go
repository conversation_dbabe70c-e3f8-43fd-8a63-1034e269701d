package auth

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMemoryTokenManager(t *testing.T) {
	logger := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	
	manager := NewMemoryTokenManager(logger)
	ctx := context.Background()

	t.Run("StoreAndGetToken", func(t *testing.T) {
		tokenInfo := &TokenInfo{
			Token:        "test-token-123",
			Type:         TokenTypeAccess,
			UserID:       "user-123",
			Username:     "testuser",
			ProviderID:   "provider-123",
			ProviderType: "jwt",
			ExpiresAt:    time.Now().Add(time.Hour),
			Roles:        []string{"user", "admin"},
			Permissions:  []string{"read", "write"},
			Scopes:       []string{"api:read", "api:write"},
			Attributes: map[string]interface{}{
				"department": "engineering",
				"level":      "senior",
			},
		}

		err := manager.StoreToken(ctx, tokenInfo)
		require.NoError(t, err)
		assert.NotEmpty(t, tokenInfo.ID)
		assert.Equal(t, TokenStatusActive, tokenInfo.Status)

		// 获取令牌
		retrieved, err := manager.GetToken(ctx, tokenInfo.ID)
		require.NoError(t, err)
		assert.Equal(t, tokenInfo.Token, retrieved.Token)
		assert.Equal(t, tokenInfo.UserID, retrieved.UserID)
		assert.Equal(t, tokenInfo.Type, retrieved.Type)
		assert.Equal(t, tokenInfo.Roles, retrieved.Roles)
		assert.Equal(t, tokenInfo.Permissions, retrieved.Permissions)
	})

	t.Run("ValidateToken", func(t *testing.T) {
		tokenInfo := &TokenInfo{
			Token:     "valid-token-456",
			Type:      TokenTypeAccess,
			UserID:    "user-456",
			ExpiresAt: time.Now().Add(time.Hour),
		}

		err := manager.StoreToken(ctx, tokenInfo)
		require.NoError(t, err)

		// 验证有效令牌
		validated, err := manager.ValidateToken(ctx, "valid-token-456")
		require.NoError(t, err)
		assert.Equal(t, tokenInfo.UserID, validated.UserID)
		assert.Equal(t, TokenStatusActive, validated.Status)

		// 验证不存在的令牌
		_, err = manager.ValidateToken(ctx, "non-existent-token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "令牌不存在")
	})

	t.Run("RevokeToken", func(t *testing.T) {
		tokenInfo := &TokenInfo{
			Token:     "revoke-token-789",
			Type:      TokenTypeAccess,
			UserID:    "user-789",
			ExpiresAt: time.Now().Add(time.Hour),
		}

		err := manager.StoreToken(ctx, tokenInfo)
		require.NoError(t, err)

		// 撤销令牌
		err = manager.RevokeToken(ctx, tokenInfo.ID)
		require.NoError(t, err)

		// 验证令牌已撤销
		retrieved, err := manager.GetToken(ctx, tokenInfo.ID)
		require.NoError(t, err)
		assert.Equal(t, TokenStatusRevoked, retrieved.Status)
		assert.NotNil(t, retrieved.RevokedAt)

		// 验证撤销的令牌无法通过验证
		_, err = manager.ValidateToken(ctx, "revoke-token-789")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "令牌状态无效")
	})

	t.Run("ExpiredToken", func(t *testing.T) {
		tokenInfo := &TokenInfo{
			Token:     "expired-token-000",
			Type:      TokenTypeAccess,
			UserID:    "user-000",
			ExpiresAt: time.Now().Add(-time.Hour), // 已过期
		}

		err := manager.StoreToken(ctx, tokenInfo)
		require.NoError(t, err)

		// 验证过期令牌
		_, err = manager.ValidateToken(ctx, "expired-token-000")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "令牌已过期")
	})

	t.Run("RefreshToken", func(t *testing.T) {
		// 创建刷新令牌
		refreshTokenInfo := &TokenInfo{
			Token:        "refresh-token-111",
			Type:         TokenTypeRefresh,
			UserID:       "user-111",
			Username:     "refreshuser",
			ProviderID:   "provider-111",
			ProviderType: "jwt",
			ExpiresAt:    time.Now().Add(24 * time.Hour),
			Roles:        []string{"user"},
			Permissions:  []string{"read"},
		}

		err := manager.StoreToken(ctx, refreshTokenInfo)
		require.NoError(t, err)

		// 刷新令牌
		newToken, err := manager.RefreshToken(ctx, "refresh-token-111")
		require.NoError(t, err)
		assert.Equal(t, TokenTypeAccess, newToken.Type)
		assert.Equal(t, refreshTokenInfo.UserID, newToken.UserID)
		assert.Equal(t, refreshTokenInfo.Username, newToken.Username)
		assert.Equal(t, refreshTokenInfo.Roles, newToken.Roles)
		assert.NotEqual(t, refreshTokenInfo.Token, newToken.Token)

		// 验证新令牌有效
		validated, err := manager.ValidateToken(ctx, newToken.Token)
		require.NoError(t, err)
		assert.Equal(t, newToken.UserID, validated.UserID)
	})

	t.Run("RevokeUserTokens", func(t *testing.T) {
		userID := "user-multi-tokens"
		
		// 创建多个令牌
		tokens := []*TokenInfo{
			{
				Token:     "user-token-1",
				Type:      TokenTypeAccess,
				UserID:    userID,
				ExpiresAt: time.Now().Add(time.Hour),
			},
			{
				Token:     "user-token-2",
				Type:      TokenTypeRefresh,
				UserID:    userID,
				ExpiresAt: time.Now().Add(24 * time.Hour),
			},
			{
				Token:     "other-user-token",
				Type:      TokenTypeAccess,
				UserID:    "other-user",
				ExpiresAt: time.Now().Add(time.Hour),
			},
		}

		for _, token := range tokens {
			err := manager.StoreToken(ctx, token)
			require.NoError(t, err)
		}

		// 撤销用户的所有令牌
		err := manager.RevokeUserTokens(ctx, userID)
		require.NoError(t, err)

		// 验证用户令牌已撤销
		_, err = manager.ValidateToken(ctx, "user-token-1")
		assert.Error(t, err)

		_, err = manager.ValidateToken(ctx, "user-token-2")
		assert.Error(t, err)

		// 验证其他用户令牌未受影响
		_, err = manager.ValidateToken(ctx, "other-user-token")
		assert.NoError(t, err)
	})

	t.Run("GetUserActiveTokens", func(t *testing.T) {
		userID := "user-active-tokens"
		
		// 创建活跃令牌
		activeToken := &TokenInfo{
			Token:     "active-token-222",
			Type:      TokenTypeAccess,
			UserID:    userID,
			ExpiresAt: time.Now().Add(time.Hour),
		}

		// 创建过期令牌
		expiredToken := &TokenInfo{
			Token:     "expired-token-333",
			Type:      TokenTypeAccess,
			UserID:    userID,
			ExpiresAt: time.Now().Add(-time.Hour),
		}

		err := manager.StoreToken(ctx, activeToken)
		require.NoError(t, err)

		err = manager.StoreToken(ctx, expiredToken)
		require.NoError(t, err)

		// 撤销过期令牌
		err = manager.RevokeToken(ctx, expiredToken.ID)
		require.NoError(t, err)

		// 获取用户活跃令牌
		activeTokens, err := manager.GetUserActiveTokens(ctx, userID)
		require.NoError(t, err)
		assert.Len(t, activeTokens, 1)
		assert.Equal(t, activeToken.Token, activeTokens[0].Token)
	})

	t.Run("TokenBlacklist", func(t *testing.T) {
		token := "blacklist-token-444"
		expiresAt := time.Now().Add(time.Hour)

		// 添加到黑名单
		err := manager.BlacklistToken(ctx, token, expiresAt)
		require.NoError(t, err)

		// 检查是否在黑名单中
		blacklisted, err := manager.IsTokenBlacklisted(ctx, token)
		require.NoError(t, err)
		assert.True(t, blacklisted)

		// 检查不在黑名单中的令牌
		blacklisted, err = manager.IsTokenBlacklisted(ctx, "not-blacklisted")
		require.NoError(t, err)
		assert.False(t, blacklisted)
	})

	t.Run("CleanupExpiredTokens", func(t *testing.T) {
		// 创建过期令牌
		expiredToken := &TokenInfo{
			Token:     "cleanup-expired-555",
			Type:      TokenTypeAccess,
			UserID:    "user-555",
			ExpiresAt: time.Now().Add(-2 * time.Hour),
		}

		// 创建有效令牌
		validToken := &TokenInfo{
			Token:     "cleanup-valid-666",
			Type:      TokenTypeAccess,
			UserID:    "user-666",
			ExpiresAt: time.Now().Add(time.Hour),
		}

		err := manager.StoreToken(ctx, expiredToken)
		require.NoError(t, err)

		err = manager.StoreToken(ctx, validToken)
		require.NoError(t, err)

		// 清理过期令牌
		err = manager.CleanupExpiredTokens(ctx)
		require.NoError(t, err)

		// 验证过期令牌已被清理
		_, err = manager.GetToken(ctx, expiredToken.ID)
		assert.Error(t, err)

		// 验证有效令牌仍然存在
		_, err = manager.GetToken(ctx, validToken.ID)
		assert.NoError(t, err)
	})

	t.Run("TokenStats", func(t *testing.T) {
		// 清理现有令牌
		err := manager.CleanupExpiredTokens(ctx)
		require.NoError(t, err)

		// 创建不同状态的令牌
		tokens := []*TokenInfo{
			{
				Token:     "stats-active-1",
				Type:      TokenTypeAccess,
				UserID:    "user-stats-1",
				ExpiresAt: time.Now().Add(time.Hour),
			},
			{
				Token:     "stats-active-2",
				Type:      TokenTypeRefresh,
				UserID:    "user-stats-2",
				ExpiresAt: time.Now().Add(time.Hour),
			},
			{
				Token:     "stats-expired",
				Type:      TokenTypeAccess,
				UserID:    "user-stats-3",
				ExpiresAt: time.Now().Add(-time.Hour),
			},
		}

		for _, token := range tokens {
			err := manager.StoreToken(ctx, token)
			require.NoError(t, err)
		}

		// 撤销一个令牌
		err = manager.RevokeToken(ctx, tokens[0].ID)
		require.NoError(t, err)

		// 获取统计信息
		stats := manager.GetTokenStats(ctx)
		assert.Equal(t, 3, stats["total_tokens"])
		assert.Equal(t, 1, stats["active_tokens"])
		assert.Equal(t, 1, stats["expired_tokens"])
		assert.Equal(t, 1, stats["revoked_tokens"])

		tokensByType := stats["tokens_by_type"].(map[TokenType]int)
		assert.Equal(t, 2, tokensByType[TokenTypeAccess])
		assert.Equal(t, 1, tokensByType[TokenTypeRefresh])
	})
}

func TestTokenGeneration(t *testing.T) {
	logger := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	
	manager := NewMemoryTokenManager(logger)

	t.Run("GenerateTokenID", func(t *testing.T) {
		id1 := manager.GenerateTokenID()
		id2 := manager.GenerateTokenID()

		assert.NotEmpty(t, id1)
		assert.NotEmpty(t, id2)
		assert.NotEqual(t, id1, id2)
		assert.Equal(t, 32, len(id1)) // 16 bytes = 32 hex chars
	})

	t.Run("GenerateTokenValue", func(t *testing.T) {
		value1 := manager.generateTokenValue()
		value2 := manager.generateTokenValue()

		assert.NotEmpty(t, value1)
		assert.NotEmpty(t, value2)
		assert.NotEqual(t, value1, value2)
		assert.Equal(t, 64, len(value1)) // 32 bytes = 64 hex chars
	})

	t.Run("HashToken", func(t *testing.T) {
		token := "abcdefghijklmnopqrstuvwxyz"
		hashed := manager.hashToken(token)

		assert.NotEqual(t, token, hashed)
		assert.Contains(t, hashed, "***")
		assert.True(t, len(hashed) < len(token))

		// 测试短令牌
		shortToken := "abc"
		shortHashed := manager.hashToken(shortToken)
		assert.Equal(t, "***", shortHashed)
	})
}
