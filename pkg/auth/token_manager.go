package auth

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// TokenType 定义令牌类型
type TokenType string

const (
	// TokenTypeAccess 访问令牌
	TokenTypeAccess TokenType = "access"
	// TokenTypeRefresh 刷新令牌
	TokenTypeRefresh TokenType = "refresh"
	// TokenTypeID ID 令牌
	TokenTypeID TokenType = "id"
)

// TokenStatus 定义令牌状态
type TokenStatus string

const (
	// TokenStatusActive 活跃状态
	TokenStatusActive TokenStatus = "active"
	// TokenStatusRevoked 已撤销状态
	TokenStatusRevoked TokenStatus = "revoked"
	// TokenStatusExpired 已过期状态
	TokenStatusExpired TokenStatus = "expired"
)

// TokenInfo 令牌信息
type TokenInfo struct {
	// 基本信息
	ID       string    `json:"id"`        // 令牌唯一标识
	Token    string    `json:"token"`     // 令牌值
	Type     TokenType `json:"type"`      // 令牌类型
	Status   TokenStatus `json:"status"`  // 令牌状态
	
	// 用户信息
	UserID   string `json:"user_id"`   // 用户ID
	Username string `json:"username"`  // 用户名
	
	// 提供者信息
	ProviderID   string `json:"provider_id"`   // ID 提供者ID
	ProviderType string `json:"provider_type"` // ID 提供者类型
	
	// 时间信息
	IssuedAt  time.Time  `json:"issued_at"`  // 签发时间
	ExpiresAt time.Time  `json:"expires_at"` // 过期时间
	RevokedAt *time.Time `json:"revoked_at,omitempty"` // 撤销时间
	
	// 权限信息
	Roles       []string               `json:"roles"`       // 角色列表
	Permissions []string               `json:"permissions"` // 权限列表
	Scopes      []string               `json:"scopes"`      // 权限范围
	Attributes  map[string]interface{} `json:"attributes"`  // 用户属性
	
	// 元数据
	Metadata map[string]string `json:"metadata"` // 元数据
}

// TokenManager 令牌管理器接口
type TokenManager interface {
	// 存储令牌
	StoreToken(ctx context.Context, tokenInfo *TokenInfo) error
	
	// 获取令牌信息
	GetToken(ctx context.Context, tokenID string) (*TokenInfo, error)
	
	// 验证令牌
	ValidateToken(ctx context.Context, token string) (*TokenInfo, error)
	
	// 刷新令牌
	RefreshToken(ctx context.Context, refreshToken string) (*TokenInfo, error)
	
	// 撤销令牌
	RevokeToken(ctx context.Context, tokenID string) error
	
	// 撤销用户的所有令牌
	RevokeUserTokens(ctx context.Context, userID string) error
	
	// 撤销提供者的所有令牌
	RevokeProviderTokens(ctx context.Context, providerID string) error
	
	// 清理过期令牌
	CleanupExpiredTokens(ctx context.Context) error
	
	// 获取用户的活跃令牌
	GetUserActiveTokens(ctx context.Context, userID string) ([]*TokenInfo, error)
	
	// 检查令牌是否在黑名单中
	IsTokenBlacklisted(ctx context.Context, token string) (bool, error)
	
	// 添加令牌到黑名单
	BlacklistToken(ctx context.Context, token string, expiresAt time.Time) error
	
	// 生成令牌ID
	GenerateTokenID() string
}

// MemoryTokenManager 内存令牌管理器实现
type MemoryTokenManager struct {
	mu         sync.RWMutex
	tokens     map[string]*TokenInfo // tokenID -> TokenInfo
	tokenIndex map[string]string     // token -> tokenID
	blacklist  map[string]time.Time  // token -> expiresAt
	logger     *telemetry.Logger
}

// NewMemoryTokenManager 创建内存令牌管理器
func NewMemoryTokenManager(logger *telemetry.Logger) *MemoryTokenManager {
	manager := &MemoryTokenManager{
		tokens:     make(map[string]*TokenInfo),
		tokenIndex: make(map[string]string),
		blacklist:  make(map[string]time.Time),
		logger:     logger.With("component", "token_manager"),
	}
	
	// 启动清理协程
	go manager.startCleanupRoutine()
	
	return manager
}

// StoreToken 存储令牌
func (m *MemoryTokenManager) StoreToken(ctx context.Context, tokenInfo *TokenInfo) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if tokenInfo.ID == "" {
		tokenInfo.ID = m.GenerateTokenID()
	}
	
	if tokenInfo.IssuedAt.IsZero() {
		tokenInfo.IssuedAt = time.Now()
	}
	
	if tokenInfo.Status == "" {
		tokenInfo.Status = TokenStatusActive
	}
	
	// 存储令牌信息
	m.tokens[tokenInfo.ID] = tokenInfo
	m.tokenIndex[tokenInfo.Token] = tokenInfo.ID
	
	m.logger.Debug("已存储令牌",
		"token_id", tokenInfo.ID,
		"user_id", tokenInfo.UserID,
		"type", tokenInfo.Type,
		"expires_at", tokenInfo.ExpiresAt)
	
	return nil
}

// GetToken 获取令牌信息
func (m *MemoryTokenManager) GetToken(ctx context.Context, tokenID string) (*TokenInfo, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	tokenInfo, exists := m.tokens[tokenID]
	if !exists {
		return nil, fmt.Errorf("令牌不存在: %s", tokenID)
	}
	
	// 返回副本以避免并发修改
	result := *tokenInfo
	return &result, nil
}

// ValidateToken 验证令牌
func (m *MemoryTokenManager) ValidateToken(ctx context.Context, token string) (*TokenInfo, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	// 检查黑名单
	if expiresAt, blacklisted := m.blacklist[token]; blacklisted {
		if time.Now().Before(expiresAt) {
			return nil, fmt.Errorf("令牌已被撤销")
		}
	}
	
	// 查找令牌
	tokenID, exists := m.tokenIndex[token]
	if !exists {
		return nil, fmt.Errorf("令牌不存在")
	}
	
	tokenInfo, exists := m.tokens[tokenID]
	if !exists {
		return nil, fmt.Errorf("令牌信息不存在")
	}
	
	// 检查状态
	if tokenInfo.Status != TokenStatusActive {
		return nil, fmt.Errorf("令牌状态无效: %s", tokenInfo.Status)
	}
	
	// 检查过期时间
	if time.Now().After(tokenInfo.ExpiresAt) {
		// 更新状态为过期
		tokenInfo.Status = TokenStatusExpired
		return nil, fmt.Errorf("令牌已过期")
	}
	
	// 返回副本
	result := *tokenInfo
	return &result, nil
}

// RefreshToken 刷新令牌
func (m *MemoryTokenManager) RefreshToken(ctx context.Context, refreshToken string) (*TokenInfo, error) {
	// 验证刷新令牌
	tokenInfo, err := m.ValidateToken(ctx, refreshToken)
	if err != nil {
		return nil, fmt.Errorf("刷新令牌验证失败: %w", err)
	}
	
	if tokenInfo.Type != TokenTypeRefresh {
		return nil, fmt.Errorf("不是有效的刷新令牌")
	}
	
	// 撤销旧的访问令牌
	if err := m.RevokeUserTokens(ctx, tokenInfo.UserID); err != nil {
		m.logger.Warn("撤销用户旧令牌失败", "user_id", tokenInfo.UserID, "error", err)
	}
	
	// 创建新的访问令牌
	newTokenInfo := &TokenInfo{
		Type:         TokenTypeAccess,
		UserID:       tokenInfo.UserID,
		Username:     tokenInfo.Username,
		ProviderID:   tokenInfo.ProviderID,
		ProviderType: tokenInfo.ProviderType,
		Roles:        tokenInfo.Roles,
		Permissions:  tokenInfo.Permissions,
		Scopes:       tokenInfo.Scopes,
		Attributes:   tokenInfo.Attributes,
		Metadata:     tokenInfo.Metadata,
		ExpiresAt:    time.Now().Add(time.Hour), // 默认1小时过期
	}
	
	// 生成新令牌值
	newTokenInfo.Token = m.generateTokenValue()
	
	// 存储新令牌
	if err := m.StoreToken(ctx, newTokenInfo); err != nil {
		return nil, fmt.Errorf("存储新令牌失败: %w", err)
	}
	
	m.logger.Info("令牌刷新成功",
		"user_id", tokenInfo.UserID,
		"old_token_id", tokenInfo.ID,
		"new_token_id", newTokenInfo.ID)
	
	return newTokenInfo, nil
}

// RevokeToken 撤销令牌
func (m *MemoryTokenManager) RevokeToken(ctx context.Context, tokenID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	tokenInfo, exists := m.tokens[tokenID]
	if !exists {
		return fmt.Errorf("令牌不存在: %s", tokenID)
	}
	
	// 更新状态
	now := time.Now()
	tokenInfo.Status = TokenStatusRevoked
	tokenInfo.RevokedAt = &now
	
	// 添加到黑名单
	m.blacklist[tokenInfo.Token] = tokenInfo.ExpiresAt
	
	m.logger.Info("已撤销令牌",
		"token_id", tokenID,
		"user_id", tokenInfo.UserID,
		"type", tokenInfo.Type)
	
	return nil
}

// RevokeUserTokens 撤销用户的所有令牌
func (m *MemoryTokenManager) RevokeUserTokens(ctx context.Context, userID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	var revokedCount int
	now := time.Now()
	
	for _, tokenInfo := range m.tokens {
		if tokenInfo.UserID == userID && tokenInfo.Status == TokenStatusActive {
			tokenInfo.Status = TokenStatusRevoked
			tokenInfo.RevokedAt = &now
			m.blacklist[tokenInfo.Token] = tokenInfo.ExpiresAt
			revokedCount++
		}
	}
	
	m.logger.Info("已撤销用户所有令牌",
		"user_id", userID,
		"revoked_count", revokedCount)
	
	return nil
}

// RevokeProviderTokens 撤销提供者的所有令牌
func (m *MemoryTokenManager) RevokeProviderTokens(ctx context.Context, providerID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	var revokedCount int
	now := time.Now()
	
	for _, tokenInfo := range m.tokens {
		if tokenInfo.ProviderID == providerID && tokenInfo.Status == TokenStatusActive {
			tokenInfo.Status = TokenStatusRevoked
			tokenInfo.RevokedAt = &now
			m.blacklist[tokenInfo.Token] = tokenInfo.ExpiresAt
			revokedCount++
		}
	}
	
	m.logger.Info("已撤销提供者所有令牌",
		"provider_id", providerID,
		"revoked_count", revokedCount)

	return nil
}

// CleanupExpiredTokens 清理过期令牌
func (m *MemoryTokenManager) CleanupExpiredTokens(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()
	var cleanedTokens, cleanedBlacklist int

	// 清理过期令牌
	for tokenID, tokenInfo := range m.tokens {
		if now.After(tokenInfo.ExpiresAt) {
			delete(m.tokens, tokenID)
			delete(m.tokenIndex, tokenInfo.Token)
			cleanedTokens++
		}
	}

	// 清理过期的黑名单条目
	for token, expiresAt := range m.blacklist {
		if now.After(expiresAt) {
			delete(m.blacklist, token)
			cleanedBlacklist++
		}
	}

	if cleanedTokens > 0 || cleanedBlacklist > 0 {
		m.logger.Info("清理过期数据完成",
			"cleaned_tokens", cleanedTokens,
			"cleaned_blacklist", cleanedBlacklist)
	}

	return nil
}

// GetUserActiveTokens 获取用户的活跃令牌
func (m *MemoryTokenManager) GetUserActiveTokens(ctx context.Context, userID string) ([]*TokenInfo, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var activeTokens []*TokenInfo
	now := time.Now()

	for _, tokenInfo := range m.tokens {
		if tokenInfo.UserID == userID &&
		   tokenInfo.Status == TokenStatusActive &&
		   now.Before(tokenInfo.ExpiresAt) {
			// 返回副本
			result := *tokenInfo
			activeTokens = append(activeTokens, &result)
		}
	}

	return activeTokens, nil
}

// IsTokenBlacklisted 检查令牌是否在黑名单中
func (m *MemoryTokenManager) IsTokenBlacklisted(ctx context.Context, token string) (bool, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	expiresAt, blacklisted := m.blacklist[token]
	if !blacklisted {
		return false, nil
	}

	// 检查黑名单条目是否过期
	if time.Now().After(expiresAt) {
		return false, nil
	}

	return true, nil
}

// BlacklistToken 添加令牌到黑名单
func (m *MemoryTokenManager) BlacklistToken(ctx context.Context, token string, expiresAt time.Time) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.blacklist[token] = expiresAt

	m.logger.Info("已添加令牌到黑名单",
		"token_hash", m.hashToken(token),
		"expires_at", expiresAt)

	return nil
}

// GenerateTokenID 生成令牌ID
func (m *MemoryTokenManager) GenerateTokenID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// generateTokenValue 生成令牌值
func (m *MemoryTokenManager) generateTokenValue() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// hashToken 对令牌进行哈希处理（用于日志记录）
func (m *MemoryTokenManager) hashToken(token string) string {
	if len(token) < 8 {
		return "***"
	}
	return token[:4] + "***" + token[len(token)-4:]
}

// startCleanupRoutine 启动清理协程
func (m *MemoryTokenManager) startCleanupRoutine() {
	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ctx := context.Background()
			if err := m.CleanupExpiredTokens(ctx); err != nil {
				m.logger.Error("清理过期令牌失败", "error", err)
			}
		}
	}
}

// GetTokenStats 获取令牌统计信息
func (m *MemoryTokenManager) GetTokenStats(ctx context.Context) map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := map[string]interface{}{
		"total_tokens":     len(m.tokens),
		"blacklist_size":   len(m.blacklist),
		"active_tokens":    0,
		"expired_tokens":   0,
		"revoked_tokens":   0,
		"tokens_by_type":   make(map[TokenType]int),
		"tokens_by_provider": make(map[string]int),
	}

	now := time.Now()
	tokensByType := make(map[TokenType]int)
	tokensByProvider := make(map[string]int)

	for _, tokenInfo := range m.tokens {
		// 统计状态
		if tokenInfo.Status == TokenStatusActive && now.Before(tokenInfo.ExpiresAt) {
			stats["active_tokens"] = stats["active_tokens"].(int) + 1
		} else if now.After(tokenInfo.ExpiresAt) {
			stats["expired_tokens"] = stats["expired_tokens"].(int) + 1
		} else if tokenInfo.Status == TokenStatusRevoked {
			stats["revoked_tokens"] = stats["revoked_tokens"].(int) + 1
		}

		// 统计类型
		tokensByType[tokenInfo.Type]++

		// 统计提供者
		tokensByProvider[tokenInfo.ProviderID]++
	}

	stats["tokens_by_type"] = tokensByType
	stats["tokens_by_provider"] = tokensByProvider

	return stats
}
