package auth

import (
	"context"
	"fmt"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// AuthResult represents the result of authentication
type AuthResult struct {
	Authenticated bool                   `json:"authenticated"`
	UserID        string                 `json:"user_id,omitempty"`
	Username      string                 `json:"username,omitempty"`
	Roles         []string               `json:"roles,omitempty"`
	Permissions   []string               `json:"permissions,omitempty"`
	Attributes    map[string]interface{} `json:"attributes,omitempty"`
	TokenType     string                 `json:"token_type,omitempty"`
	ExpiresAt     *time.Time             `json:"expires_at,omitempty"`
	Error         string                 `json:"error,omitempty"`
}

// AuthContext contains authentication context information
type AuthContext struct {
	RequestID    string                 `json:"request_id"`
	ClientIP     string                 `json:"client_ip"`
	UserAgent    string                 `json:"user_agent"`
	Path         string                 `json:"path"`
	Method       string                 `json:"method"`
	Headers      map[string]string      `json:"headers"`
	Timestamp    time.Time              `json:"timestamp"`
	DeviceInfo   map[string]interface{} `json:"device_info,omitempty"`
	GeoLocation  map[string]interface{} `json:"geo_location,omitempty"`
}

// Authenticator interface defines authentication methods
type Authenticator interface {
	Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error)
	Name() string
	Priority() int
	Enabled() bool
}

// Manager manages authentication operations
type Manager struct {
	config         *config.AuthConfig
	logger         *telemetry.Logger
	authenticators []Authenticator

	// Authentication providers
	jwtAuth    *JWTAuthenticator
	oidcAuth   *OIDCAuthenticator
	apiKeyAuth *APIKeyAuthenticator
	mtlsAuth   *MTLSAuthenticator

	// Policy engine
	policyEngine PolicyEngine

	// Enhanced components
	idProviderManager IDProviderManager
	tokenManager      TokenManager
	rbacManager       RBACManager
}

// NewManager creates a new authentication manager
func NewManager(cfg config.AuthConfig, logger *telemetry.Logger) (*Manager, error) {
	manager := &Manager{
		config:         &cfg,
		logger:         logger,
		authenticators: make([]Authenticator, 0),
	}

	// Initialize enhanced components
	manager.idProviderManager = NewDefaultIDProviderManager(logger)
	manager.tokenManager = NewMemoryTokenManager(logger)
	manager.rbacManager = NewMemoryRBACManager(logger)

	// Initialize authenticators based on configuration
	if err := manager.initializeAuthenticators(); err != nil {
		return nil, fmt.Errorf("failed to initialize authenticators: %w", err)
	}

	// Initialize policy engine
	if err := manager.initializePolicyEngine(); err != nil {
		return nil, fmt.Errorf("failed to initialize policy engine: %w", err)
	}

	// Initialize default ID providers from configuration
	if err := manager.initializeDefaultProviders(); err != nil {
		return nil, fmt.Errorf("failed to initialize default providers: %w", err)
	}

	return manager, nil
}

// initializeAuthenticators initializes all configured authenticators
func (m *Manager) initializeAuthenticators() error {
	// Initialize JWT authenticator
	if m.config.JWT.Enabled {
		jwtAuth, err := NewJWTAuthenticator(m.config.JWT, m.logger.With("auth", "jwt"))
		if err != nil {
			return fmt.Errorf("failed to initialize JWT authenticator: %w", err)
		}
		m.jwtAuth = jwtAuth
		m.authenticators = append(m.authenticators, jwtAuth)
	}

	// Initialize OIDC authenticator
	if m.config.OIDC.Enabled {
		oidcAuth, err := NewOIDCAuthenticator(m.config.OIDC, m.logger.With("auth", "oidc"))
		if err != nil {
			return fmt.Errorf("failed to initialize OIDC authenticator: %w", err)
		}
		m.oidcAuth = oidcAuth
		m.authenticators = append(m.authenticators, oidcAuth)
	}

	// Initialize API Key authenticator
	if m.config.APIKey.Enabled {
		apiKeyAuth, err := NewAPIKeyAuthenticator(m.config.APIKey, m.logger.With("auth", "apikey"))
		if err != nil {
			return fmt.Errorf("failed to initialize API Key authenticator: %w", err)
		}
		m.apiKeyAuth = apiKeyAuth
		m.authenticators = append(m.authenticators, apiKeyAuth)
	}

	// Initialize mTLS authenticator
	if m.config.MTLS.Enabled {
		mtlsAuth, err := NewMTLSAuthenticator(m.config.MTLS, m.logger.With("auth", "mtls"))
		if err != nil {
			return fmt.Errorf("failed to initialize mTLS authenticator: %w", err)
		}
		m.mtlsAuth = mtlsAuth
		m.authenticators = append(m.authenticators, mtlsAuth)
	}

	m.logger.Info("Initialized authenticators", "count", len(m.authenticators))
	return nil
}

// initializePolicyEngine initializes the policy engine
func (m *Manager) initializePolicyEngine() error {
	var err error
	
	switch m.config.Policies.Engine {
	case "opa":
		m.policyEngine, err = NewOPAPolicyEngine(m.config.Policies, m.logger.With("component", "policy"))
	case "builtin":
		m.policyEngine, err = NewBuiltinPolicyEngine(m.config.Policies, m.logger.With("component", "policy"))
	default:
		return fmt.Errorf("unsupported policy engine: %s", m.config.Policies.Engine)
	}

	if err != nil {
		return fmt.Errorf("failed to initialize policy engine: %w", err)
	}

	return nil
}

// Authenticate performs authentication using all configured authenticators
func (m *Manager) Authenticate(ctx context.Context, authCtx *AuthContext) (*AuthResult, error) {
	// Try each authenticator in priority order
	for _, authenticator := range m.authenticators {
		if !authenticator.Enabled() {
			continue
		}

		result, err := authenticator.Authenticate(ctx, authCtx)
		if err != nil {
			m.logger.Error("Authentication error",
				"authenticator", authenticator.Name(),
				"request_id", authCtx.RequestID,
				"error", err)
			continue
		}

		if result.Authenticated {
			m.logger.Info("Authentication successful",
				"authenticator", authenticator.Name(),
				"user_id", result.UserID,
				"request_id", authCtx.RequestID)
			return result, nil
		}
	}

	// No authenticator succeeded
	result := &AuthResult{
		Authenticated: false,
		Error:         "authentication failed",
	}

	m.logger.Warn("Authentication failed",
		"request_id", authCtx.RequestID,
		"client_ip", authCtx.ClientIP,
		"path", authCtx.Path)

	return result, nil
}

// Authorize performs authorization using the policy engine
func (m *Manager) Authorize(ctx context.Context, authResult *AuthResult, authCtx *AuthContext) (bool, error) {
	if !authResult.Authenticated {
		return false, fmt.Errorf("user not authenticated")
	}

	// Create authorization context
	authzCtx := &AuthorizationContext{
		UserID:      authResult.UserID,
		Username:    authResult.Username,
		Roles:       authResult.Roles,
		Permissions: authResult.Permissions,
		Attributes:  authResult.Attributes,
		Resource:    authCtx.Path,
		Action:      authCtx.Method,
		Context:     authCtx,
	}

	// Check authorization
	decision, err := m.policyEngine.Evaluate(ctx, authzCtx)
	if err != nil {
		m.logger.Error("Authorization evaluation error",
			"user_id", authResult.UserID,
			"resource", authCtx.Path,
			"action", authCtx.Method,
			"error", err)
		return false, fmt.Errorf("authorization evaluation failed: %w", err)
	}

	if decision.Allowed {
		m.logger.Info("Authorization granted",
			"user_id", authResult.UserID,
			"resource", authCtx.Path,
			"action", authCtx.Method,
			"policy", decision.Policy)
	} else {
		m.logger.Warn("Authorization denied",
			"user_id", authResult.UserID,
			"resource", authCtx.Path,
			"action", authCtx.Method,
			"reason", decision.Reason)
	}

	return decision.Allowed, nil
}

// ValidateToken validates a token using the appropriate authenticator
func (m *Manager) ValidateToken(ctx context.Context, token, tokenType string) (*AuthResult, error) {
	switch tokenType {
	case "jwt", "bearer":
		if m.jwtAuth != nil {
			return m.jwtAuth.ValidateToken(ctx, token)
		}
	case "apikey":
		if m.apiKeyAuth != nil {
			return m.apiKeyAuth.ValidateAPIKey(ctx, token)
		}
	}

	return &AuthResult{
		Authenticated: false,
		Error:         "unsupported token type",
	}, nil
}

// RefreshToken refreshes an authentication token
func (m *Manager) RefreshToken(ctx context.Context, refreshToken string) (*AuthResult, error) {
	// Try JWT refresh first
	if m.jwtAuth != nil {
		result, err := m.jwtAuth.RefreshToken(ctx, refreshToken)
		if err == nil && result.Authenticated {
			return result, nil
		}
	}

	// Try OIDC refresh
	if m.oidcAuth != nil {
		result, err := m.oidcAuth.RefreshToken(ctx, refreshToken)
		if err == nil && result.Authenticated {
			return result, nil
		}
	}

	return &AuthResult{
		Authenticated: false,
		Error:         "token refresh failed",
	}, fmt.Errorf("unable to refresh token")
}

// RevokeToken revokes an authentication token
func (m *Manager) RevokeToken(ctx context.Context, token, tokenType string) error {
	switch tokenType {
	case "jwt", "bearer":
		if m.jwtAuth != nil {
			return m.jwtAuth.RevokeToken(ctx, token)
		}
	case "apikey":
		if m.apiKeyAuth != nil {
			return m.apiKeyAuth.RevokeAPIKey(ctx, token)
		}
	}

	return fmt.Errorf("unsupported token type for revocation: %s", tokenType)
}

// GetUserInfo retrieves user information
func (m *Manager) GetUserInfo(ctx context.Context, userID string) (map[string]interface{}, error) {
	// This would typically query a user store or identity provider
	// For now, return basic information
	userInfo := map[string]interface{}{
		"user_id": userID,
		"active":  true,
	}

	return userInfo, nil
}

// CreateAuthContext creates an authentication context from request information
func (m *Manager) CreateAuthContext(requestID, clientIP, userAgent, path, method string, headers map[string]string) *AuthContext {
	return &AuthContext{
		RequestID: requestID,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Path:      path,
		Method:    method,
		Headers:   headers,
		Timestamp: time.Now(),
	}
}

// GetAuthenticators returns all configured authenticators
func (m *Manager) GetAuthenticators() []Authenticator {
	return m.authenticators
}

// GetPolicyEngine returns the policy engine
func (m *Manager) GetPolicyEngine() PolicyEngine {
	return m.policyEngine
}

// UpdateConfig updates the authentication configuration
func (m *Manager) UpdateConfig(cfg config.AuthConfig) error {
	m.config = &cfg

	// Reinitialize authenticators with new configuration
	if err := m.initializeAuthenticators(); err != nil {
		return fmt.Errorf("failed to reinitialize authenticators: %w", err)
	}

	// Reinitialize policy engine with new configuration
	if err := m.initializePolicyEngine(); err != nil {
		return fmt.Errorf("failed to reinitialize policy engine: %w", err)
	}

	m.logger.Info("Authentication configuration updated successfully")
	return nil
}

// Close closes the authentication manager and all its components
func (m *Manager) Close() error {
	// Close policy engine
	if m.policyEngine != nil {
		if err := m.policyEngine.Close(); err != nil {
			m.logger.Error("Failed to close policy engine", "error", err)
		}
	}

	// Close authenticators
	for _, auth := range m.authenticators {
		if closer, ok := auth.(interface{ Close() error }); ok {
			if err := closer.Close(); err != nil {
				m.logger.Error("Failed to close authenticator",
					"authenticator", auth.Name(), "error", err)
			}
		}
	}

	return nil
}

// initializeDefaultProviders 初始化默认 ID 提供者
func (m *Manager) initializeDefaultProviders() error {
	ctx := context.Background()

	// 从配置中初始化 JWT 提供者
	if m.config.JWT.Enabled {
		jwtProvider := &IDProviderConfig{
			ID:          "default-jwt",
			Name:        "默认 JWT 提供者",
			Type:        IDProviderTypeJWT,
			Description: "从配置文件初始化的默认 JWT 认证提供者",
			Status:      IDProviderStatusActive,
			Priority:    10,
			Enabled:     true,
			Config: map[string]interface{}{
				"secret":     m.config.JWT.Secret,
				"public_key": m.config.JWT.PublicKey,
				"algorithm":  m.config.JWT.Algorithm,
				"expiration": m.config.JWT.Expiration,
			},
			Metadata: map[string]string{
				"source": "config",
			},
		}

		if err := m.idProviderManager.RegisterProvider(ctx, jwtProvider); err != nil {
			m.logger.Warn("注册默认 JWT 提供者失败", "error", err)
		}
	}

	// 从配置中初始化 OIDC 提供者
	if m.config.OIDC.Enabled {
		oidcProvider := &IDProviderConfig{
			ID:          "default-oidc",
			Name:        "默认 OIDC 提供者",
			Type:        IDProviderTypeOIDC,
			Description: "从配置文件初始化的默认 OIDC 认证提供者",
			Status:      IDProviderStatusActive,
			Priority:    20,
			Enabled:     true,
			Config: map[string]interface{}{
				"issuer":        m.config.OIDC.Issuer,
				"client_id":     m.config.OIDC.ClientID,
				"client_secret": m.config.OIDC.ClientSecret,
				"redirect_url":  m.config.OIDC.RedirectURL,
				"scopes":        m.config.OIDC.Scopes,
				"skip_verify":   m.config.OIDC.SkipVerify,
			},
			Metadata: map[string]string{
				"source": "config",
			},
		}

		if err := m.idProviderManager.RegisterProvider(ctx, oidcProvider); err != nil {
			m.logger.Warn("注册默认 OIDC 提供者失败", "error", err)
		}
	}

	// 从配置中初始化 API Key 提供者
	if m.config.APIKey.Enabled {
		apiKeyProvider := &IDProviderConfig{
			ID:          "default-apikey",
			Name:        "默认 API Key 提供者",
			Type:        IDProviderTypeAPIKey,
			Description: "从配置文件初始化的默认 API Key 认证提供者",
			Status:      IDProviderStatusActive,
			Priority:    30,
			Enabled:     true,
			Config: map[string]interface{}{
				"header_name":  m.config.APIKey.HeaderName,
				"query_param":  m.config.APIKey.QueryParam,
			},
			Metadata: map[string]string{
				"source": "config",
			},
		}

		if err := m.idProviderManager.RegisterProvider(ctx, apiKeyProvider); err != nil {
			m.logger.Warn("注册默认 API Key 提供者失败", "error", err)
		}
	}

	// 从配置中初始化 mTLS 提供者
	if m.config.MTLS.Enabled {
		mtlsProvider := &IDProviderConfig{
			ID:          "default-mtls",
			Name:        "默认 mTLS 提供者",
			Type:        IDProviderTypeMTLS,
			Description: "从配置文件初始化的默认 mTLS 认证提供者",
			Status:      IDProviderStatusActive,
			Priority:    40,
			Enabled:     true,
			Config: map[string]interface{}{
				"ca_file":                   m.config.MTLS.CAFile,
				"crl_file":                  m.config.MTLS.CRLFile,
				"ocsp_enabled":              m.config.MTLS.OCSPEnabled,
				"ocsp_timeout_seconds":      m.config.MTLS.OCSPTimeoutSeconds,
				"verify_client_cert_cn":     m.config.MTLS.VerifyClientCertCN,
				"allowed_cns":               m.config.MTLS.AllowedCNs,
				"verify_organization":       m.config.MTLS.VerifyOrganization,
				"allowed_organizations":     m.config.MTLS.AllowedOrganizations,
			},
			Metadata: map[string]string{
				"source": "config",
			},
		}

		if err := m.idProviderManager.RegisterProvider(ctx, mtlsProvider); err != nil {
			m.logger.Warn("注册默认 mTLS 提供者失败", "error", err)
		}
	}

	m.logger.Info("默认 ID 提供者初始化完成")
	return nil
}

// GetIDProviderManager 获取 ID 提供者管理器
func (m *Manager) GetIDProviderManager() IDProviderManager {
	return m.idProviderManager
}

// GetTokenManager 获取令牌管理器
func (m *Manager) GetTokenManager() TokenManager {
	return m.tokenManager
}

// GetRBACManager 获取 RBAC 管理器
func (m *Manager) GetRBACManager() RBACManager {
	return m.rbacManager
}
