package auth

import (
	"context"
	"testing"
	"time"

	"api-gateway/pkg/telemetry"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDefaultIDProviderManager(t *testing.T) {
	logger := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	
	manager := NewDefaultIDProviderManager(logger)
	ctx := context.Background()

	t.Run("RegisterProvider", func(t *testing.T) {
		config := &IDProviderConfig{
			ID:          "test-jwt",
			Name:        "测试 JWT 提供者",
			Type:        IDProviderTypeJWT,
			Description: "用于测试的 JWT 提供者",
			Status:      IDProviderStatusActive,
			Priority:    10,
			Enabled:     true,
			Config: map[string]interface{}{
				"secret":     "test-secret",
				"algorithm":  "HS256",
				"expiration": 3600,
			},
		}

		err := manager.RegisterProvider(ctx, config)
		require.NoError(t, err)

		// 验证提供者已注册
		provider, err := manager.GetProvider(ctx, "test-jwt")
		require.NoError(t, err)
		assert.Equal(t, "test-jwt", provider.ID)
		assert.Equal(t, "测试 JWT 提供者", provider.Name)
		assert.Equal(t, IDProviderTypeJWT, provider.Type)
		assert.True(t, provider.Enabled)
	})

	t.Run("RegisterDuplicateProvider", func(t *testing.T) {
		config := &IDProviderConfig{
			ID:   "test-jwt",
			Name: "重复的提供者",
			Type: IDProviderTypeJWT,
			Config: map[string]interface{}{
				"secret": "test-secret",
			},
		}

		err := manager.RegisterProvider(ctx, config)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "已存在")
	})

	t.Run("UpdateProvider", func(t *testing.T) {
		config := &IDProviderConfig{
			ID:          "test-jwt",
			Name:        "更新的 JWT 提供者",
			Type:        IDProviderTypeJWT,
			Description: "更新后的描述",
			Status:      IDProviderStatusActive,
			Priority:    20,
			Enabled:     false,
			Config: map[string]interface{}{
				"secret":     "updated-secret",
				"algorithm":  "HS512",
				"expiration": 7200,
			},
		}

		err := manager.UpdateProvider(ctx, "test-jwt", config)
		require.NoError(t, err)

		// 验证更新
		provider, err := manager.GetProvider(ctx, "test-jwt")
		require.NoError(t, err)
		assert.Equal(t, "更新的 JWT 提供者", provider.Name)
		assert.Equal(t, "更新后的描述", provider.Description)
		assert.False(t, provider.Enabled)
		assert.Equal(t, 20, provider.Priority)
	})

	t.Run("EnableDisableProvider", func(t *testing.T) {
		// 启用提供者
		err := manager.EnableProvider(ctx, "test-jwt")
		require.NoError(t, err)

		provider, err := manager.GetProvider(ctx, "test-jwt")
		require.NoError(t, err)
		assert.True(t, provider.Enabled)
		assert.Equal(t, IDProviderStatusActive, provider.Status)

		// 禁用提供者
		err = manager.DisableProvider(ctx, "test-jwt")
		require.NoError(t, err)

		provider, err = manager.GetProvider(ctx, "test-jwt")
		require.NoError(t, err)
		assert.False(t, provider.Enabled)
		assert.Equal(t, IDProviderStatusInactive, provider.Status)
	})

	t.Run("ListProviders", func(t *testing.T) {
		providers, err := manager.ListProviders(ctx)
		require.NoError(t, err)
		assert.Len(t, providers, 1)
		assert.Equal(t, "test-jwt", providers[0].ID)
	})

	t.Run("GetActiveAuthenticators", func(t *testing.T) {
		// 启用提供者
		err := manager.EnableProvider(ctx, "test-jwt")
		require.NoError(t, err)

		authenticators, err := manager.GetActiveAuthenticators(ctx)
		require.NoError(t, err)
		assert.Len(t, authenticators, 1)
		assert.Equal(t, "JWT", authenticators[0].Name())
	})

	t.Run("UnregisterProvider", func(t *testing.T) {
		err := manager.UnregisterProvider(ctx, "test-jwt")
		require.NoError(t, err)

		// 验证提供者已删除
		_, err = manager.GetProvider(ctx, "test-jwt")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不存在")

		// 验证列表为空
		providers, err := manager.ListProviders(ctx)
		require.NoError(t, err)
		assert.Len(t, providers, 0)
	})

	t.Run("ValidateProviderConfig", func(t *testing.T) {
		// 有效配置
		validConfig := &IDProviderConfig{
			ID:   "valid-provider",
			Name: "有效提供者",
			Type: IDProviderTypeJWT,
			Config: map[string]interface{}{
				"secret": "test-secret",
			},
		}

		err := manager.ValidateProviderConfig(ctx, validConfig)
		assert.NoError(t, err)

		// 无效配置 - 缺少 ID
		invalidConfig := &IDProviderConfig{
			Name: "无效提供者",
			Type: IDProviderTypeJWT,
		}

		err = manager.ValidateProviderConfig(ctx, invalidConfig)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "ID 不能为空")

		// 无效配置 - 不支持的类型
		invalidTypeConfig := &IDProviderConfig{
			ID:   "invalid-type",
			Name: "不支持的类型",
			Type: IDProviderType("unsupported"),
		}

		err = manager.ValidateProviderConfig(ctx, invalidTypeConfig)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "不支持的 ID 提供者类型")
	})
}

func TestIDProviderConfigValidation(t *testing.T) {
	logger := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	
	manager := NewDefaultIDProviderManager(logger)
	ctx := context.Background()

	t.Run("JWTProviderValidation", func(t *testing.T) {
		// 有效的 JWT 配置
		validConfig := &IDProviderConfig{
			ID:   "jwt-valid",
			Name: "有效 JWT",
			Type: IDProviderTypeJWT,
			Config: map[string]interface{}{
				"secret":     "test-secret",
				"algorithm":  "HS256",
				"expiration": 3600,
			},
		}

		err := manager.RegisterProvider(ctx, validConfig)
		assert.NoError(t, err)

		// 无效的 JWT 配置 - 缺少密钥
		invalidConfig := &IDProviderConfig{
			ID:   "jwt-invalid",
			Name: "无效 JWT",
			Type: IDProviderTypeJWT,
			Config: map[string]interface{}{
				"algorithm": "HS256",
			},
		}

		err = manager.RegisterProvider(ctx, invalidConfig)
		assert.Error(t, err)
	})

	t.Run("OIDCProviderValidation", func(t *testing.T) {
		// 有效的 OIDC 配置
		validConfig := &IDProviderConfig{
			ID:   "oidc-valid",
			Name: "有效 OIDC",
			Type: IDProviderTypeOIDC,
			Config: map[string]interface{}{
				"issuer":        "https://example.com",
				"client_id":     "test-client",
				"client_secret": "test-secret",
			},
		}

		err := manager.RegisterProvider(ctx, validConfig)
		assert.NoError(t, err)

		// 无效的 OIDC 配置 - 缺少必需字段
		invalidConfig := &IDProviderConfig{
			ID:   "oidc-invalid",
			Name: "无效 OIDC",
			Type: IDProviderTypeOIDC,
			Config: map[string]interface{}{
				"issuer": "https://example.com",
			},
		}

		err = manager.RegisterProvider(ctx, invalidConfig)
		assert.Error(t, err)
	})
}

func TestIDProviderTypes(t *testing.T) {
	logger := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	
	manager := NewDefaultIDProviderManager(logger)

	t.Run("GetSupportedProviderTypes", func(t *testing.T) {
		types := manager.GetSupportedProviderTypes()
		assert.Contains(t, types, IDProviderTypeJWT)
		assert.Contains(t, types, IDProviderTypeOIDC)
		assert.Contains(t, types, IDProviderTypeAPIKey)
		assert.Contains(t, types, IDProviderTypeMTLS)
	})

	t.Run("GetProviderConfigSchema", func(t *testing.T) {
		schema, err := manager.GetProviderConfigSchema(IDProviderTypeJWT)
		require.NoError(t, err)
		assert.NotNil(t, schema)
		assert.Equal(t, "object", schema["type"])

		// 测试不支持的类型
		_, err = manager.GetProviderConfigSchema(IDProviderType("unsupported"))
		assert.Error(t, err)
	})
}

func TestIDProviderPriority(t *testing.T) {
	logger := telemetry.NewLogger(telemetry.LoggingConfig{
		Level:  "debug",
		Format: "text",
		Output: "stdout",
	})
	
	manager := NewDefaultIDProviderManager(logger)
	ctx := context.Background()

	// 注册多个提供者，优先级不同
	providers := []*IDProviderConfig{
		{
			ID:       "high-priority",
			Name:     "高优先级",
			Type:     IDProviderTypeJWT,
			Priority: 1,
			Enabled:  true,
			Config:   map[string]interface{}{"secret": "secret1"},
		},
		{
			ID:       "low-priority",
			Name:     "低优先级",
			Type:     IDProviderTypeJWT,
			Priority: 10,
			Enabled:  true,
			Config:   map[string]interface{}{"secret": "secret2"},
		},
		{
			ID:       "medium-priority",
			Name:     "中等优先级",
			Type:     IDProviderTypeJWT,
			Priority: 5,
			Enabled:  true,
			Config:   map[string]interface{}{"secret": "secret3"},
		},
	}

	for _, provider := range providers {
		err := manager.RegisterProvider(ctx, provider)
		require.NoError(t, err)
	}

	// 获取活跃认证器，应该按优先级排序
	authenticators, err := manager.GetActiveAuthenticators(ctx)
	require.NoError(t, err)
	assert.Len(t, authenticators, 3)

	// 验证优先级排序（数字越小优先级越高）
	assert.Equal(t, 1, authenticators[0].Priority())
	assert.Equal(t, 5, authenticators[1].Priority())
	assert.Equal(t, 10, authenticators[2].Priority())
}
