package auth

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"api-gateway/pkg/telemetry"
)

// Permission 权限定义
type Permission struct {
	ID          string            `json:"id"`          // 权限唯一标识
	Name        string            `json:"name"`        // 权限名称
	Description string            `json:"description"` // 权限描述
	Resource    string            `json:"resource"`    // 资源类型
	Action      string            `json:"action"`      // 操作类型
	Conditions  map[string]string `json:"conditions"`  // 权限条件
	Metadata    map[string]string `json:"metadata"`    // 元数据
	CreatedAt   time.Time         `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`  // 更新时间
}

// Role 角色定义
type Role struct {
	ID          string      `json:"id"`          // 角色唯一标识
	Name        string      `json:"name"`        // 角色名称
	Description string      `json:"description"` // 角色描述
	Permissions []string    `json:"permissions"` // 权限ID列表
	ParentRoles []string    `json:"parent_roles"` // 父角色ID列表（角色继承）
	Metadata    map[string]string `json:"metadata"` // 元数据
	CreatedAt   time.Time   `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time   `json:"updated_at"`  // 更新时间
}

// User 用户定义
type User struct {
	ID          string            `json:"id"`          // 用户唯一标识
	Username    string            `json:"username"`    // 用户名
	Email       string            `json:"email"`       // 邮箱
	DisplayName string            `json:"display_name"` // 显示名称
	Roles       []string          `json:"roles"`       // 角色ID列表
	Permissions []string          `json:"permissions"` // 直接分配的权限ID列表
	Attributes  map[string]interface{} `json:"attributes"` // 用户属性
	TenantID    string            `json:"tenant_id"`   // 租户ID（多租户支持）
	Status      string            `json:"status"`      // 用户状态
	Metadata    map[string]string `json:"metadata"`    // 元数据
	CreatedAt   time.Time         `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`  // 更新时间
}

// Tenant 租户定义（多租户支持）
type Tenant struct {
	ID          string            `json:"id"`          // 租户唯一标识
	Name        string            `json:"name"`        // 租户名称
	Description string            `json:"description"` // 租户描述
	Status      string            `json:"status"`      // 租户状态
	Settings    map[string]interface{} `json:"settings"` // 租户设置
	Metadata    map[string]string `json:"metadata"`    // 元数据
	CreatedAt   time.Time         `json:"created_at"`  // 创建时间
	UpdatedAt   time.Time         `json:"updated_at"`  // 更新时间
}

// PermissionCache 权限缓存条目
type PermissionCache struct {
	UserID      string                 `json:"user_id"`
	TenantID    string                 `json:"tenant_id"`
	Permissions map[string]*Permission `json:"permissions"`
	Roles       map[string]*Role       `json:"roles"`
	ExpiresAt   time.Time              `json:"expires_at"`
}

// RBACManager RBAC 管理器接口
type RBACManager interface {
	// 权限管理
	CreatePermission(ctx context.Context, permission *Permission) error
	UpdatePermission(ctx context.Context, permissionID string, permission *Permission) error
	DeletePermission(ctx context.Context, permissionID string) error
	GetPermission(ctx context.Context, permissionID string) (*Permission, error)
	ListPermissions(ctx context.Context, tenantID string) ([]*Permission, error)
	
	// 角色管理
	CreateRole(ctx context.Context, role *Role) error
	UpdateRole(ctx context.Context, roleID string, role *Role) error
	DeleteRole(ctx context.Context, roleID string) error
	GetRole(ctx context.Context, roleID string) (*Role, error)
	ListRoles(ctx context.Context, tenantID string) ([]*Role, error)
	
	// 用户管理
	CreateUser(ctx context.Context, user *User) error
	UpdateUser(ctx context.Context, userID string, user *User) error
	DeleteUser(ctx context.Context, userID string) error
	GetUser(ctx context.Context, userID string) (*User, error)
	ListUsers(ctx context.Context, tenantID string) ([]*User, error)
	
	// 租户管理
	CreateTenant(ctx context.Context, tenant *Tenant) error
	UpdateTenant(ctx context.Context, tenantID string, tenant *Tenant) error
	DeleteTenant(ctx context.Context, tenantID string) error
	GetTenant(ctx context.Context, tenantID string) (*Tenant, error)
	ListTenants(ctx context.Context) ([]*Tenant, error)
	
	// 权限分配
	AssignRoleToUser(ctx context.Context, userID, roleID string) error
	RemoveRoleFromUser(ctx context.Context, userID, roleID string) error
	AssignPermissionToUser(ctx context.Context, userID, permissionID string) error
	RemovePermissionFromUser(ctx context.Context, userID, permissionID string) error
	AssignPermissionToRole(ctx context.Context, roleID, permissionID string) error
	RemovePermissionFromRole(ctx context.Context, roleID, permissionID string) error
	
	// 权限检查
	CheckPermission(ctx context.Context, userID, resource, action string) (bool, error)
	GetUserPermissions(ctx context.Context, userID string) ([]*Permission, error)
	GetUserRoles(ctx context.Context, userID string) ([]*Role, error)
	
	// 缓存管理
	InvalidateUserCache(ctx context.Context, userID string) error
	InvalidateAllCache(ctx context.Context) error
}

// MemoryRBACManager 内存 RBAC 管理器实现
type MemoryRBACManager struct {
	mu          sync.RWMutex
	permissions map[string]*Permission // permissionID -> Permission
	roles       map[string]*Role       // roleID -> Role
	users       map[string]*User       // userID -> User
	tenants     map[string]*Tenant     // tenantID -> Tenant
	cache       map[string]*PermissionCache // userID -> PermissionCache
	logger      *telemetry.Logger
	
	// 缓存配置
	cacheEnabled bool
	cacheTTL     time.Duration
}

// NewMemoryRBACManager 创建内存 RBAC 管理器
func NewMemoryRBACManager(logger *telemetry.Logger) *MemoryRBACManager {
	manager := &MemoryRBACManager{
		permissions:  make(map[string]*Permission),
		roles:        make(map[string]*Role),
		users:        make(map[string]*User),
		tenants:      make(map[string]*Tenant),
		cache:        make(map[string]*PermissionCache),
		logger:       logger.With("component", "rbac_manager"),
		cacheEnabled: true,
		cacheTTL:     time.Hour, // 默认缓存1小时
	}
	
	// 启动缓存清理协程
	go manager.startCacheCleanup()
	
	return manager
}

// CreatePermission 创建权限
func (m *MemoryRBACManager) CreatePermission(ctx context.Context, permission *Permission) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if permission.ID == "" {
		return fmt.Errorf("权限ID不能为空")
	}
	
	if _, exists := m.permissions[permission.ID]; exists {
		return fmt.Errorf("权限 %s 已存在", permission.ID)
	}
	
	now := time.Now()
	permission.CreatedAt = now
	permission.UpdatedAt = now
	
	m.permissions[permission.ID] = permission
	
	m.logger.Info("已创建权限",
		"permission_id", permission.ID,
		"name", permission.Name,
		"resource", permission.Resource,
		"action", permission.Action)
	
	return nil
}

// UpdatePermission 更新权限
func (m *MemoryRBACManager) UpdatePermission(ctx context.Context, permissionID string, permission *Permission) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	existingPermission, exists := m.permissions[permissionID]
	if !exists {
		return fmt.Errorf("权限 %s 不存在", permissionID)
	}
	
	// 保持原有的创建时间和ID
	permission.ID = permissionID
	permission.CreatedAt = existingPermission.CreatedAt
	permission.UpdatedAt = time.Now()
	
	m.permissions[permissionID] = permission
	
	// 清除相关缓存
	m.invalidateAllCacheUnsafe()
	
	m.logger.Info("已更新权限",
		"permission_id", permissionID,
		"name", permission.Name)
	
	return nil
}

// DeletePermission 删除权限
func (m *MemoryRBACManager) DeletePermission(ctx context.Context, permissionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	permission, exists := m.permissions[permissionID]
	if !exists {
		return fmt.Errorf("权限 %s 不存在", permissionID)
	}
	
	// 从所有角色中移除此权限
	for _, role := range m.roles {
		for i, perm := range role.Permissions {
			if perm == permissionID {
				role.Permissions = append(role.Permissions[:i], role.Permissions[i+1:]...)
				break
			}
		}
	}
	
	// 从所有用户中移除此权限
	for _, user := range m.users {
		for i, perm := range user.Permissions {
			if perm == permissionID {
				user.Permissions = append(user.Permissions[:i], user.Permissions[i+1:]...)
				break
			}
		}
	}
	
	delete(m.permissions, permissionID)
	
	// 清除相关缓存
	m.invalidateAllCacheUnsafe()
	
	m.logger.Info("已删除权限",
		"permission_id", permissionID,
		"name", permission.Name)
	
	return nil
}

// GetPermission 获取权限
func (m *MemoryRBACManager) GetPermission(ctx context.Context, permissionID string) (*Permission, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	permission, exists := m.permissions[permissionID]
	if !exists {
		return nil, fmt.Errorf("权限 %s 不存在", permissionID)
	}
	
	// 返回副本
	result := *permission
	return &result, nil
}

// ListPermissions 列出权限
func (m *MemoryRBACManager) ListPermissions(ctx context.Context, tenantID string) ([]*Permission, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var permissions []*Permission
	for _, permission := range m.permissions {
		// 如果指定了租户ID，只返回该租户的权限
		if tenantID != "" {
			// 这里可以根据实际需求实现租户过滤逻辑
			// 暂时返回所有权限
		}
		
		// 返回副本
		result := *permission
		permissions = append(permissions, &result)
	}
	
	return permissions, nil
}

// CreateRole 创建角色
func (m *MemoryRBACManager) CreateRole(ctx context.Context, role *Role) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if role.ID == "" {
		return fmt.Errorf("角色ID不能为空")
	}

	if _, exists := m.roles[role.ID]; exists {
		return fmt.Errorf("角色 %s 已存在", role.ID)
	}

	// 验证权限是否存在
	for _, permissionID := range role.Permissions {
		if _, exists := m.permissions[permissionID]; !exists {
			return fmt.Errorf("权限 %s 不存在", permissionID)
		}
	}

	// 验证父角色是否存在
	for _, parentRoleID := range role.ParentRoles {
		if _, exists := m.roles[parentRoleID]; !exists {
			return fmt.Errorf("父角色 %s 不存在", parentRoleID)
		}
	}

	now := time.Now()
	role.CreatedAt = now
	role.UpdatedAt = now

	m.roles[role.ID] = role

	// 清除相关缓存
	m.invalidateAllCacheUnsafe()

	m.logger.Info("已创建角色",
		"role_id", role.ID,
		"name", role.Name,
		"permissions_count", len(role.Permissions))

	return nil
}

// UpdateRole 更新角色
func (m *MemoryRBACManager) UpdateRole(ctx context.Context, roleID string, role *Role) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	existingRole, exists := m.roles[roleID]
	if !exists {
		return fmt.Errorf("角色 %s 不存在", roleID)
	}

	// 验证权限是否存在
	for _, permissionID := range role.Permissions {
		if _, exists := m.permissions[permissionID]; !exists {
			return fmt.Errorf("权限 %s 不存在", permissionID)
		}
	}

	// 验证父角色是否存在（排除自己）
	for _, parentRoleID := range role.ParentRoles {
		if parentRoleID == roleID {
			return fmt.Errorf("角色不能继承自己")
		}
		if _, exists := m.roles[parentRoleID]; !exists {
			return fmt.Errorf("父角色 %s 不存在", parentRoleID)
		}
	}

	// 检查循环继承
	if m.hasCircularInheritance(roleID, role.ParentRoles) {
		return fmt.Errorf("检测到循环角色继承")
	}

	// 保持原有的创建时间和ID
	role.ID = roleID
	role.CreatedAt = existingRole.CreatedAt
	role.UpdatedAt = time.Now()

	m.roles[roleID] = role

	// 清除相关缓存
	m.invalidateAllCacheUnsafe()

	m.logger.Info("已更新角色",
		"role_id", roleID,
		"name", role.Name)

	return nil
}

// DeleteRole 删除角色
func (m *MemoryRBACManager) DeleteRole(ctx context.Context, roleID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	role, exists := m.roles[roleID]
	if !exists {
		return fmt.Errorf("角色 %s 不存在", roleID)
	}

	// 从所有用户中移除此角色
	for _, user := range m.users {
		for i, userRole := range user.Roles {
			if userRole == roleID {
				user.Roles = append(user.Roles[:i], user.Roles[i+1:]...)
				break
			}
		}
	}

	// 从所有角色的父角色列表中移除此角色
	for _, otherRole := range m.roles {
		for i, parentRole := range otherRole.ParentRoles {
			if parentRole == roleID {
				otherRole.ParentRoles = append(otherRole.ParentRoles[:i], otherRole.ParentRoles[i+1:]...)
				break
			}
		}
	}

	delete(m.roles, roleID)

	// 清除相关缓存
	m.invalidateAllCacheUnsafe()

	m.logger.Info("已删除角色",
		"role_id", roleID,
		"name", role.Name)

	return nil
}

// GetRole 获取角色
func (m *MemoryRBACManager) GetRole(ctx context.Context, roleID string) (*Role, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	role, exists := m.roles[roleID]
	if !exists {
		return nil, fmt.Errorf("角色 %s 不存在", roleID)
	}

	// 返回副本
	result := *role
	return &result, nil
}

// ListRoles 列出角色
func (m *MemoryRBACManager) ListRoles(ctx context.Context, tenantID string) ([]*Role, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var roles []*Role
	for _, role := range m.roles {
		// 如果指定了租户ID，只返回该租户的角色
		if tenantID != "" {
			// 这里可以根据实际需求实现租户过滤逻辑
			// 暂时返回所有角色
		}

		// 返回副本
		result := *role
		roles = append(roles, &result)
	}

	return roles, nil
}

// hasCircularInheritance 检查是否存在循环继承
func (m *MemoryRBACManager) hasCircularInheritance(roleID string, parentRoles []string) bool {
	visited := make(map[string]bool)

	var checkCircular func(string) bool
	checkCircular = func(currentRoleID string) bool {
		if visited[currentRoleID] {
			return true // 发现循环
		}

		visited[currentRoleID] = true

		// 检查当前角色的父角色
		var parentsToCheck []string
		if currentRoleID == roleID {
			// 对于正在更新的角色，使用新的父角色列表
			parentsToCheck = parentRoles
		} else {
			// 对于其他角色，使用现有的父角色列表
			if role, exists := m.roles[currentRoleID]; exists {
				parentsToCheck = role.ParentRoles
			}
		}

		for _, parentRoleID := range parentsToCheck {
			if checkCircular(parentRoleID) {
				return true
			}
		}

		visited[currentRoleID] = false
		return false
	}

	return checkCircular(roleID)
}

// CreateUser 创建用户
func (m *MemoryRBACManager) CreateUser(ctx context.Context, user *User) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if user.ID == "" {
		return fmt.Errorf("用户ID不能为空")
	}

	if _, exists := m.users[user.ID]; exists {
		return fmt.Errorf("用户 %s 已存在", user.ID)
	}

	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	m.users[user.ID] = user

	// 清除相关缓存
	m.invalidateAllCacheUnsafe()

	m.logger.Info("已创建用户",
		"user_id", user.ID,
		"username", user.Username)

	return nil
}

// UpdateUser 更新用户
func (m *MemoryRBACManager) UpdateUser(ctx context.Context, userID string, user *User) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	existingUser, exists := m.users[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不存在", userID)
	}

	// 保持原有的创建时间和ID
	user.ID = userID
	user.CreatedAt = existingUser.CreatedAt
	user.UpdatedAt = time.Now()

	m.users[userID] = user

	// 清除相关缓存
	m.invalidateUserCacheUnsafe(userID)

	m.logger.Info("已更新用户",
		"user_id", userID,
		"username", user.Username)

	return nil
}

// DeleteUser 删除用户
func (m *MemoryRBACManager) DeleteUser(ctx context.Context, userID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	user, exists := m.users[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不存在", userID)
	}

	delete(m.users, userID)

	// 清除相关缓存
	m.invalidateUserCacheUnsafe(userID)

	m.logger.Info("已删除用户",
		"user_id", userID,
		"username", user.Username)

	return nil
}

// GetUser 获取用户
func (m *MemoryRBACManager) GetUser(ctx context.Context, userID string) (*User, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	user, exists := m.users[userID]
	if !exists {
		return nil, fmt.Errorf("用户 %s 不存在", userID)
	}

	// 返回副本
	result := *user
	return &result, nil
}

// ListUsers 列出用户
func (m *MemoryRBACManager) ListUsers(ctx context.Context, tenantID string) ([]*User, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var users []*User
	for _, user := range m.users {
		// 如果指定了租户ID，只返回该租户的用户
		if tenantID != "" && user.TenantID != tenantID {
			continue
		}

		// 返回副本
		result := *user
		users = append(users, &result)
	}

	return users, nil
}

// CreateTenant 创建租户
func (m *MemoryRBACManager) CreateTenant(ctx context.Context, tenant *Tenant) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if tenant.ID == "" {
		return fmt.Errorf("租户ID不能为空")
	}

	if _, exists := m.tenants[tenant.ID]; exists {
		return fmt.Errorf("租户 %s 已存在", tenant.ID)
	}

	now := time.Now()
	tenant.CreatedAt = now
	tenant.UpdatedAt = now

	m.tenants[tenant.ID] = tenant

	m.logger.Info("已创建租户",
		"tenant_id", tenant.ID,
		"name", tenant.Name)

	return nil
}

// UpdateTenant 更新租户
func (m *MemoryRBACManager) UpdateTenant(ctx context.Context, tenantID string, tenant *Tenant) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	existingTenant, exists := m.tenants[tenantID]
	if !exists {
		return fmt.Errorf("租户 %s 不存在", tenantID)
	}

	// 保持原有的创建时间和ID
	tenant.ID = tenantID
	tenant.CreatedAt = existingTenant.CreatedAt
	tenant.UpdatedAt = time.Now()

	m.tenants[tenantID] = tenant

	m.logger.Info("已更新租户",
		"tenant_id", tenantID,
		"name", tenant.Name)

	return nil
}

// DeleteTenant 删除租户
func (m *MemoryRBACManager) DeleteTenant(ctx context.Context, tenantID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	tenant, exists := m.tenants[tenantID]
	if !exists {
		return fmt.Errorf("租户 %s 不存在", tenantID)
	}

	delete(m.tenants, tenantID)

	m.logger.Info("已删除租户",
		"tenant_id", tenantID,
		"name", tenant.Name)

	return nil
}

// GetTenant 获取租户
func (m *MemoryRBACManager) GetTenant(ctx context.Context, tenantID string) (*Tenant, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	tenant, exists := m.tenants[tenantID]
	if !exists {
		return nil, fmt.Errorf("租户 %s 不存在", tenantID)
	}

	// 返回副本
	result := *tenant
	return &result, nil
}

// ListTenants 列出租户
func (m *MemoryRBACManager) ListTenants(ctx context.Context) ([]*Tenant, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var tenants []*Tenant
	for _, tenant := range m.tenants {
		// 返回副本
		result := *tenant
		tenants = append(tenants, &result)
	}

	return tenants, nil
}

// AssignRoleToUser 为用户分配角色
func (m *MemoryRBACManager) AssignRoleToUser(ctx context.Context, userID, roleID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	user, exists := m.users[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不存在", userID)
	}

	if _, exists := m.roles[roleID]; !exists {
		return fmt.Errorf("角色 %s 不存在", roleID)
	}

	// 检查是否已经分配
	for _, existingRole := range user.Roles {
		if existingRole == roleID {
			return fmt.Errorf("用户 %s 已经拥有角色 %s", userID, roleID)
		}
	}

	user.Roles = append(user.Roles, roleID)
	user.UpdatedAt = time.Now()

	// 清除相关缓存
	m.invalidateUserCacheUnsafe(userID)

	m.logger.Info("已为用户分配角色",
		"user_id", userID,
		"role_id", roleID)

	return nil
}

// RemoveRoleFromUser 从用户移除角色
func (m *MemoryRBACManager) RemoveRoleFromUser(ctx context.Context, userID, roleID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	user, exists := m.users[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不存在", userID)
	}

	// 查找并移除角色
	for i, existingRole := range user.Roles {
		if existingRole == roleID {
			user.Roles = append(user.Roles[:i], user.Roles[i+1:]...)
			user.UpdatedAt = time.Now()

			// 清除相关缓存
			m.invalidateUserCacheUnsafe(userID)

			m.logger.Info("已从用户移除角色",
				"user_id", userID,
				"role_id", roleID)

			return nil
		}
	}

	return fmt.Errorf("用户 %s 没有角色 %s", userID, roleID)
}

// AssignPermissionToUser 为用户分配权限
func (m *MemoryRBACManager) AssignPermissionToUser(ctx context.Context, userID, permissionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	user, exists := m.users[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不存在", userID)
	}

	if _, exists := m.permissions[permissionID]; !exists {
		return fmt.Errorf("权限 %s 不存在", permissionID)
	}

	// 检查是否已经分配
	for _, existingPermission := range user.Permissions {
		if existingPermission == permissionID {
			return fmt.Errorf("用户 %s 已经拥有权限 %s", userID, permissionID)
		}
	}

	user.Permissions = append(user.Permissions, permissionID)
	user.UpdatedAt = time.Now()

	// 清除相关缓存
	m.invalidateUserCacheUnsafe(userID)

	m.logger.Info("已为用户分配权限",
		"user_id", userID,
		"permission_id", permissionID)

	return nil
}

// RemovePermissionFromUser 从用户移除权限
func (m *MemoryRBACManager) RemovePermissionFromUser(ctx context.Context, userID, permissionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	user, exists := m.users[userID]
	if !exists {
		return fmt.Errorf("用户 %s 不存在", userID)
	}

	// 查找并移除权限
	for i, existingPermission := range user.Permissions {
		if existingPermission == permissionID {
			user.Permissions = append(user.Permissions[:i], user.Permissions[i+1:]...)
			user.UpdatedAt = time.Now()

			// 清除相关缓存
			m.invalidateUserCacheUnsafe(userID)

			m.logger.Info("已从用户移除权限",
				"user_id", userID,
				"permission_id", permissionID)

			return nil
		}
	}

	return fmt.Errorf("用户 %s 没有权限 %s", userID, permissionID)
}

// AssignPermissionToRole 为角色分配权限
func (m *MemoryRBACManager) AssignPermissionToRole(ctx context.Context, roleID, permissionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	role, exists := m.roles[roleID]
	if !exists {
		return fmt.Errorf("角色 %s 不存在", roleID)
	}

	if _, exists := m.permissions[permissionID]; !exists {
		return fmt.Errorf("权限 %s 不存在", permissionID)
	}

	// 检查是否已经分配
	for _, existingPermission := range role.Permissions {
		if existingPermission == permissionID {
			return fmt.Errorf("角色 %s 已经拥有权限 %s", roleID, permissionID)
		}
	}

	role.Permissions = append(role.Permissions, permissionID)
	role.UpdatedAt = time.Now()

	// 清除相关缓存
	m.invalidateAllCacheUnsafe()

	m.logger.Info("已为角色分配权限",
		"role_id", roleID,
		"permission_id", permissionID)

	return nil
}

// RemovePermissionFromRole 从角色移除权限
func (m *MemoryRBACManager) RemovePermissionFromRole(ctx context.Context, roleID, permissionID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	role, exists := m.roles[roleID]
	if !exists {
		return fmt.Errorf("角色 %s 不存在", roleID)
	}

	// 查找并移除权限
	for i, existingPermission := range role.Permissions {
		if existingPermission == permissionID {
			role.Permissions = append(role.Permissions[:i], role.Permissions[i+1:]...)
			role.UpdatedAt = time.Now()

			// 清除相关缓存
			m.invalidateAllCacheUnsafe()

			m.logger.Info("已从角色移除权限",
				"role_id", roleID,
				"permission_id", permissionID)

			return nil
		}
	}

	return fmt.Errorf("角色 %s 没有权限 %s", roleID, permissionID)
}

// CheckPermission 检查用户权限
func (m *MemoryRBACManager) CheckPermission(ctx context.Context, userID, resource, action string) (bool, error) {
	// 首先尝试从缓存获取
	if m.cacheEnabled {
		if cached := m.getUserPermissionCache(userID); cached != nil {
			return m.checkPermissionFromCache(cached, resource, action), nil
		}
	}

	// 从数据库获取用户权限
	permissions, err := m.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	// 检查权限
	for _, permission := range permissions {
		if m.matchesPermission(permission, resource, action) {
			return true, nil
		}
	}

	return false, nil
}

// GetUserPermissions 获取用户的所有权限
func (m *MemoryRBACManager) GetUserPermissions(ctx context.Context, userID string) ([]*Permission, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	user, exists := m.users[userID]
	if !exists {
		return nil, fmt.Errorf("用户 %s 不存在", userID)
	}

	permissionMap := make(map[string]*Permission)

	// 获取直接分配的权限
	for _, permissionID := range user.Permissions {
		if permission, exists := m.permissions[permissionID]; exists {
			permissionMap[permissionID] = permission
		}
	}

	// 获取角色权限
	for _, roleID := range user.Roles {
		rolePermissions := m.getRolePermissionsRecursive(roleID, make(map[string]bool))
		for _, permission := range rolePermissions {
			permissionMap[permission.ID] = permission
		}
	}

	// 转换为切片
	var permissions []*Permission
	for _, permission := range permissionMap {
		// 返回副本
		result := *permission
		permissions = append(permissions, &result)
	}

	// 缓存权限
	if m.cacheEnabled {
		m.cacheUserPermissions(userID, permissions)
	}

	return permissions, nil
}

// GetUserRoles 获取用户的所有角色
func (m *MemoryRBACManager) GetUserRoles(ctx context.Context, userID string) ([]*Role, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	user, exists := m.users[userID]
	if !exists {
		return nil, fmt.Errorf("用户 %s 不存在", userID)
	}

	var roles []*Role
	for _, roleID := range user.Roles {
		if role, exists := m.roles[roleID]; exists {
			// 返回副本
			result := *role
			roles = append(roles, &result)
		}
	}

	return roles, nil
}

// InvalidateUserCache 清除用户缓存
func (m *MemoryRBACManager) InvalidateUserCache(ctx context.Context, userID string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.invalidateUserCacheUnsafe(userID)
	return nil
}

// InvalidateAllCache 清除所有缓存
func (m *MemoryRBACManager) InvalidateAllCache(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.invalidateAllCacheUnsafe()
	return nil
}

// 内部方法

// getRolePermissionsRecursive 递归获取角色权限（包括继承的权限）
func (m *MemoryRBACManager) getRolePermissionsRecursive(roleID string, visited map[string]bool) []*Permission {
	if visited[roleID] {
		return nil // 防止循环引用
	}
	visited[roleID] = true

	role, exists := m.roles[roleID]
	if !exists {
		return nil
	}

	var permissions []*Permission

	// 获取直接权限
	for _, permissionID := range role.Permissions {
		if permission, exists := m.permissions[permissionID]; exists {
			permissions = append(permissions, permission)
		}
	}

	// 获取继承的权限
	for _, parentRoleID := range role.ParentRoles {
		parentPermissions := m.getRolePermissionsRecursive(parentRoleID, visited)
		permissions = append(permissions, parentPermissions...)
	}

	return permissions
}

// matchesPermission 检查权限是否匹配
func (m *MemoryRBACManager) matchesPermission(permission *Permission, resource, action string) bool {
	// 精确匹配
	if permission.Resource == resource && permission.Action == action {
		return true
	}

	// 通配符匹配
	if permission.Resource == "*" || permission.Action == "*" {
		return true
	}

	// 前缀匹配
	if strings.HasSuffix(permission.Resource, "*") {
		prefix := strings.TrimSuffix(permission.Resource, "*")
		if strings.HasPrefix(resource, prefix) {
			return permission.Action == action || permission.Action == "*"
		}
	}

	return false
}

// getUserPermissionCache 获取用户权限缓存
func (m *MemoryRBACManager) getUserPermissionCache(userID string) *PermissionCache {
	cached, exists := m.cache[userID]
	if !exists {
		return nil
	}

	// 检查是否过期
	if time.Now().After(cached.ExpiresAt) {
		delete(m.cache, userID)
		return nil
	}

	return cached
}

// checkPermissionFromCache 从缓存检查权限
func (m *MemoryRBACManager) checkPermissionFromCache(cached *PermissionCache, resource, action string) bool {
	for _, permission := range cached.Permissions {
		if m.matchesPermission(permission, resource, action) {
			return true
		}
	}
	return false
}

// cacheUserPermissions 缓存用户权限
func (m *MemoryRBACManager) cacheUserPermissions(userID string, permissions []*Permission) {
	permissionMap := make(map[string]*Permission)
	for _, permission := range permissions {
		permissionMap[permission.ID] = permission
	}

	cached := &PermissionCache{
		UserID:      userID,
		Permissions: permissionMap,
		ExpiresAt:   time.Now().Add(m.cacheTTL),
	}

	m.cache[userID] = cached
}

// invalidateUserCacheUnsafe 清除用户缓存（不加锁）
func (m *MemoryRBACManager) invalidateUserCacheUnsafe(userID string) {
	delete(m.cache, userID)
}

// invalidateAllCacheUnsafe 清除所有缓存（不加锁）
func (m *MemoryRBACManager) invalidateAllCacheUnsafe() {
	m.cache = make(map[string]*PermissionCache)
}

// startCacheCleanup 启动缓存清理协程
func (m *MemoryRBACManager) startCacheCleanup() {
	ticker := time.NewTicker(time.Hour) // 每小时清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			m.cleanupExpiredCache()
		}
	}
}

// cleanupExpiredCache 清理过期缓存
func (m *MemoryRBACManager) cleanupExpiredCache() {
	m.mu.Lock()
	defer m.mu.Unlock()

	now := time.Now()
	var cleanedCount int

	for userID, cached := range m.cache {
		if now.After(cached.ExpiresAt) {
			delete(m.cache, userID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		m.logger.Info("清理过期权限缓存完成", "cleaned_count", cleanedCount)
	}
}
