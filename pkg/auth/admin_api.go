package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"api-gateway/pkg/telemetry"

	"github.com/gin-gonic/gin"
)

// AdminAPI 认证管理 API
type AdminAPI struct {
	idProviderManager IDProviderManager
	tokenManager      TokenManager
	rbacManager       RBACManager
	logger            *telemetry.Logger
}

// NewAdminAPI 创建认证管理 API
func NewAdminAPI(
	idProviderManager IDProviderManager,
	tokenManager TokenManager,
	rbacManager RBACManager,
	logger *telemetry.Logger,
) *AdminAPI {
	return &AdminAPI{
		idProviderManager: idProviderManager,
		tokenManager:      tokenManager,
		rbacManager:       rbacManager,
		logger:            logger.With("component", "auth_admin_api"),
	}
}

// RegisterRoutes 注册路由
func (api *AdminAPI) RegisterRoutes(router *gin.RouterGroup) {
	// ID Provider 管理路由
	providers := router.Group("/providers")
	{
		providers.GET("", api.ListProviders)
		providers.POST("", api.CreateProvider)
		providers.GET("/:id", api.GetProvider)
		providers.PUT("/:id", api.UpdateProvider)
		providers.DELETE("/:id", api.DeleteProvider)
		providers.POST("/:id/enable", api.EnableProvider)
		providers.POST("/:id/disable", api.DisableProvider)
		providers.POST("/:id/test", api.TestProvider)
		providers.GET("/types", api.GetSupportedProviderTypes)
		providers.GET("/types/:type/schema", api.GetProviderConfigSchema)
	}
	
	// 令牌管理路由
	tokens := router.Group("/tokens")
	{
		tokens.GET("", api.ListTokens)
		tokens.GET("/:id", api.GetToken)
		tokens.DELETE("/:id", api.RevokeToken)
		tokens.POST("/:id/refresh", api.RefreshToken)
		tokens.DELETE("/user/:user_id", api.RevokeUserTokens)
		tokens.DELETE("/provider/:provider_id", api.RevokeProviderTokens)
		tokens.GET("/stats", api.GetTokenStats)
		tokens.POST("/cleanup", api.CleanupExpiredTokens)
	}
	
	// 权限管理路由
	permissions := router.Group("/permissions")
	{
		permissions.GET("", api.ListPermissions)
		permissions.POST("", api.CreatePermission)
		permissions.GET("/:id", api.GetPermission)
		permissions.PUT("/:id", api.UpdatePermission)
		permissions.DELETE("/:id", api.DeletePermission)
	}
	
	// 角色管理路由
	roles := router.Group("/roles")
	{
		roles.GET("", api.ListRoles)
		roles.POST("", api.CreateRole)
		roles.GET("/:id", api.GetRole)
		roles.PUT("/:id", api.UpdateRole)
		roles.DELETE("/:id", api.DeleteRole)
		roles.POST("/:id/permissions/:permission_id", api.AssignPermissionToRole)
		roles.DELETE("/:id/permissions/:permission_id", api.RemovePermissionFromRole)
	}
	
	// 用户管理路由
	users := router.Group("/users")
	{
		users.GET("", api.ListUsers)
		users.POST("", api.CreateUser)
		users.GET("/:id", api.GetUser)
		users.PUT("/:id", api.UpdateUser)
		users.DELETE("/:id", api.DeleteUser)
		users.POST("/:id/roles/:role_id", api.AssignRoleToUser)
		users.DELETE("/:id/roles/:role_id", api.RemoveRoleFromUser)
		users.POST("/:id/permissions/:permission_id", api.AssignPermissionToUser)
		users.DELETE("/:id/permissions/:permission_id", api.RemovePermissionFromUser)
		users.GET("/:id/permissions", api.GetUserPermissions)
		users.GET("/:id/roles", api.GetUserRoles)
		users.GET("/:id/tokens", api.GetUserTokens)
	}
	
	// 租户管理路由
	tenants := router.Group("/tenants")
	{
		tenants.GET("", api.ListTenants)
		tenants.POST("", api.CreateTenant)
		tenants.GET("/:id", api.GetTenant)
		tenants.PUT("/:id", api.UpdateTenant)
		tenants.DELETE("/:id", api.DeleteTenant)
	}
}

// ID Provider 管理 API

// ListProviders 列出所有 ID 提供者
// @Summary 列出所有 ID 提供者
// @Description 获取所有已注册的身份提供者列表
// @Tags ID Provider
// @Accept json
// @Produce json
// @Success 200 {array} IDProviderConfig
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers [get]
func (api *AdminAPI) ListProviders(c *gin.Context) {
	ctx := c.Request.Context()
	
	providers, err := api.idProviderManager.ListProviders(ctx)
	if err != nil {
		api.logger.Error("列出 ID 提供者失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"providers": providers})
}

// CreateProvider 创建 ID 提供者
// @Summary 创建 ID 提供者
// @Description 注册新的身份提供者
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param provider body IDProviderConfig true "ID 提供者配置"
// @Success 201 {object} IDProviderConfig
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers [post]
func (api *AdminAPI) CreateProvider(c *gin.Context) {
	ctx := c.Request.Context()
	
	var config IDProviderConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}
	
	if err := api.idProviderManager.RegisterProvider(ctx, &config); err != nil {
		api.logger.Error("创建 ID 提供者失败", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusCreated, gin.H{"provider": config})
}

// GetProvider 获取 ID 提供者
// @Summary 获取 ID 提供者
// @Description 根据 ID 获取身份提供者详情
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param id path string true "提供者 ID"
// @Success 200 {object} IDProviderConfig
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers/{id} [get]
func (api *AdminAPI) GetProvider(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	
	provider, err := api.idProviderManager.GetProvider(ctx, id)
	if err != nil {
		api.logger.Error("获取 ID 提供者失败", "id", id, "error", err)
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"provider": provider})
}

// UpdateProvider 更新 ID 提供者
// @Summary 更新 ID 提供者
// @Description 更新身份提供者配置
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param id path string true "提供者 ID"
// @Param provider body IDProviderConfig true "ID 提供者配置"
// @Success 200 {object} IDProviderConfig
// @Failure 400 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers/{id} [put]
func (api *AdminAPI) UpdateProvider(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	
	var config IDProviderConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}
	
	if err := api.idProviderManager.UpdateProvider(ctx, id, &config); err != nil {
		api.logger.Error("更新 ID 提供者失败", "id", id, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"provider": config})
}

// DeleteProvider 删除 ID 提供者
// @Summary 删除 ID 提供者
// @Description 注销身份提供者
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param id path string true "提供者 ID"
// @Success 204
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers/{id} [delete]
func (api *AdminAPI) DeleteProvider(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	
	if err := api.idProviderManager.UnregisterProvider(ctx, id); err != nil {
		api.logger.Error("删除 ID 提供者失败", "id", id, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.Status(http.StatusNoContent)
}

// EnableProvider 启用 ID 提供者
// @Summary 启用 ID 提供者
// @Description 启用指定的身份提供者
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param id path string true "提供者 ID"
// @Success 200 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers/{id}/enable [post]
func (api *AdminAPI) EnableProvider(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	
	if err := api.idProviderManager.EnableProvider(ctx, id); err != nil {
		api.logger.Error("启用 ID 提供者失败", "id", id, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "ID 提供者已启用"})
}

// DisableProvider 禁用 ID 提供者
// @Summary 禁用 ID 提供者
// @Description 禁用指定的身份提供者
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param id path string true "提供者 ID"
// @Success 200 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers/{id}/disable [post]
func (api *AdminAPI) DisableProvider(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	
	if err := api.idProviderManager.DisableProvider(ctx, id); err != nil {
		api.logger.Error("禁用 ID 提供者失败", "id", id, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "ID 提供者已禁用"})
}

// TestProvider 测试 ID 提供者连接
// @Summary 测试 ID 提供者连接
// @Description 测试身份提供者的连接状态
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param id path string true "提供者 ID"
// @Success 200 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/providers/{id}/test [post]
func (api *AdminAPI) TestProvider(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.Param("id")
	
	if err := api.idProviderManager.TestProvider(ctx, id); err != nil {
		api.logger.Error("测试 ID 提供者失败", "id", id, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{"message": "连接测试成功"})
}

// GetSupportedProviderTypes 获取支持的提供者类型
// @Summary 获取支持的提供者类型
// @Description 获取系统支持的所有身份提供者类型
// @Tags ID Provider
// @Accept json
// @Produce json
// @Success 200 {array} string
// @Router /admin/auth/providers/types [get]
func (api *AdminAPI) GetSupportedProviderTypes(c *gin.Context) {
	if manager, ok := api.idProviderManager.(*DefaultIDProviderManager); ok {
		types := manager.GetSupportedProviderTypes()
		c.JSON(http.StatusOK, gin.H{"types": types})
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "不支持的管理器类型"})
	}
}

// GetProviderConfigSchema 获取提供者配置模式
// @Summary 获取提供者配置模式
// @Description 获取指定类型身份提供者的配置模式
// @Tags ID Provider
// @Accept json
// @Produce json
// @Param type path string true "提供者类型"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]string
// @Router /admin/auth/providers/types/{type}/schema [get]
func (api *AdminAPI) GetProviderConfigSchema(c *gin.Context) {
	providerType := IDProviderType(c.Param("type"))

	if manager, ok := api.idProviderManager.(*DefaultIDProviderManager); ok {
		schema, err := manager.GetProviderConfigSchema(providerType)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{"schema": schema})
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "不支持的管理器类型"})
	}
}

// 令牌管理 API

// ListTokens 列出令牌
// @Summary 列出令牌
// @Description 获取令牌列表（支持分页和过滤）
// @Tags Token
// @Accept json
// @Produce json
// @Param user_id query string false "用户ID过滤"
// @Param provider_id query string false "提供者ID过滤"
// @Param status query string false "状态过滤"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(20)
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} map[string]string
// @Router /admin/auth/tokens [get]
func (api *AdminAPI) ListTokens(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取查询参数
	userID := c.Query("user_id")
	providerID := c.Query("provider_id")
	status := c.Query("status")

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 这里需要实现分页和过滤逻辑
	// 由于当前的 TokenManager 接口没有直接支持，我们先返回基本信息
	if userID != "" {
		tokens, err := api.tokenManager.GetUserActiveTokens(ctx, userID)
		if err != nil {
			api.logger.Error("获取用户令牌失败", "user_id", userID, "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"tokens": tokens,
			"total":  len(tokens),
			"page":   page,
			"limit":  limit,
		})
		return
	}

	// 获取令牌统计信息
	if manager, ok := api.tokenManager.(*MemoryTokenManager); ok {
		stats := manager.GetTokenStats(ctx)
		c.JSON(http.StatusOK, gin.H{
			"stats": stats,
			"page":  page,
			"limit": limit,
		})
	} else {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "不支持的令牌管理器类型"})
	}
}

// GetToken 获取令牌详情
// @Summary 获取令牌详情
// @Description 根据令牌ID获取详细信息
// @Tags Token
// @Accept json
// @Produce json
// @Param id path string true "令牌ID"
// @Success 200 {object} TokenInfo
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/tokens/{id} [get]
func (api *AdminAPI) GetToken(c *gin.Context) {
	ctx := c.Request.Context()
	tokenID := c.Param("id")

	token, err := api.tokenManager.GetToken(ctx, tokenID)
	if err != nil {
		api.logger.Error("获取令牌失败", "token_id", tokenID, "error", err)
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"token": token})
}

// RevokeToken 撤销令牌
// @Summary 撤销令牌
// @Description 撤销指定的令牌
// @Tags Token
// @Accept json
// @Produce json
// @Param id path string true "令牌ID"
// @Success 200 {object} map[string]string
// @Failure 404 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/tokens/{id} [delete]
func (api *AdminAPI) RevokeToken(c *gin.Context) {
	ctx := c.Request.Context()
	tokenID := c.Param("id")

	if err := api.tokenManager.RevokeToken(ctx, tokenID); err != nil {
		api.logger.Error("撤销令牌失败", "token_id", tokenID, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "令牌已撤销"})
}

// RefreshToken 刷新令牌
// @Summary 刷新令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags Token
// @Accept json
// @Produce json
// @Param id path string true "刷新令牌ID"
// @Success 200 {object} TokenInfo
// @Failure 400 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/tokens/{id}/refresh [post]
func (api *AdminAPI) RefreshToken(c *gin.Context) {
	ctx := c.Request.Context()
	tokenID := c.Param("id")

	// 首先获取令牌信息
	tokenInfo, err := api.tokenManager.GetToken(ctx, tokenID)
	if err != nil {
		api.logger.Error("获取令牌失败", "token_id", tokenID, "error", err)
		c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
		return
	}

	// 刷新令牌
	newToken, err := api.tokenManager.RefreshToken(ctx, tokenInfo.Token)
	if err != nil {
		api.logger.Error("刷新令牌失败", "token_id", tokenID, "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"token": newToken})
}

// RevokeUserTokens 撤销用户所有令牌
// @Summary 撤销用户所有令牌
// @Description 撤销指定用户的所有令牌
// @Tags Token
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/tokens/user/{user_id} [delete]
func (api *AdminAPI) RevokeUserTokens(c *gin.Context) {
	ctx := c.Request.Context()
	userID := c.Param("user_id")

	if err := api.tokenManager.RevokeUserTokens(ctx, userID); err != nil {
		api.logger.Error("撤销用户令牌失败", "user_id", userID, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "用户所有令牌已撤销"})
}

// RevokeProviderTokens 撤销提供者所有令牌
// @Summary 撤销提供者所有令牌
// @Description 撤销指定提供者的所有令牌
// @Tags Token
// @Accept json
// @Produce json
// @Param provider_id path string true "提供者ID"
// @Success 200 {object} map[string]string
// @Failure 500 {object} map[string]string
// @Router /admin/auth/tokens/provider/{provider_id} [delete]
func (api *AdminAPI) RevokeProviderTokens(c *gin.Context) {
	ctx := c.Request.Context()
	providerID := c.Param("provider_id")

	if err := api.tokenManager.RevokeProviderTokens(ctx, providerID); err != nil {
		api.logger.Error("撤销提供者令牌失败", "provider_id", providerID, "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "提供者所有令牌已撤销"})
}
