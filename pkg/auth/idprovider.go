package auth

import (
	"context"
	"fmt"
	"sync"
	"time"

	"api-gateway/pkg/config"
	"api-gateway/pkg/telemetry"
)

// IDProviderType 定义身份提供者类型
type IDProviderType string

const (
	// IDProviderTypeJWT JWT 身份提供者
	IDProviderTypeJWT IDProviderType = "jwt"
	// IDProviderTypeOIDC OIDC 身份提供者
	IDProviderTypeOIDC IDProviderType = "oidc"
	// IDProviderTypeOAuth2 OAuth2 身份提供者
	IDProviderTypeOAuth2 IDProviderType = "oauth2"
	// IDProviderTypeSAML SAML 身份提供者
	IDProviderTypeSAML IDProviderType = "saml"
	// IDProviderTypeLDAP LDAP 身份提供者
	IDProviderTypeLDAP IDProviderType = "ldap"
	// IDProviderTypeAPIKey API Key 身份提供者
	IDProviderTypeAPIKey IDProviderType = "apikey"
	// IDProviderTypeMTLS mTLS 身份提供者
	IDProviderTypeMTLS IDProviderType = "mtls"
)

// IDProviderStatus 定义身份提供者状态
type IDProviderStatus string

const (
	// IDProviderStatusActive 活跃状态
	IDProviderStatusActive IDProviderStatus = "active"
	// IDProviderStatusInactive 非活跃状态
	IDProviderStatusInactive IDProviderStatus = "inactive"
	// IDProviderStatusError 错误状态
	IDProviderStatusError IDProviderStatus = "error"
	// IDProviderStatusPending 待处理状态
	IDProviderStatusPending IDProviderStatus = "pending"
)

// IDProviderConfig 身份提供者配置
type IDProviderConfig struct {
	// 基本信息
	ID          string                 `json:"id" yaml:"id"`                     // 唯一标识符
	Name        string                 `json:"name" yaml:"name"`                 // 显示名称
	Type        IDProviderType         `json:"type" yaml:"type"`                 // 提供者类型
	Description string                 `json:"description" yaml:"description"`   // 描述信息
	
	// 状态和优先级
	Status   IDProviderStatus `json:"status" yaml:"status"`     // 状态
	Priority int              `json:"priority" yaml:"priority"` // 优先级（数字越小优先级越高）
	Enabled  bool             `json:"enabled" yaml:"enabled"`   // 是否启用
	
	// 配置信息
	Config map[string]interface{} `json:"config" yaml:"config"` // 具体配置
	
	// 元数据
	Metadata map[string]string `json:"metadata" yaml:"metadata"` // 元数据
	
	// 时间戳
	CreatedAt time.Time `json:"created_at" yaml:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at" yaml:"updated_at"` // 更新时间
	
	// 认证器实例（运行时）
	authenticator Authenticator `json:"-" yaml:"-"`
}

// IDProviderManager ID 提供者管理器接口
type IDProviderManager interface {
	// 注册 ID 提供者
	RegisterProvider(ctx context.Context, config *IDProviderConfig) error
	
	// 注销 ID 提供者
	UnregisterProvider(ctx context.Context, id string) error
	
	// 更新 ID 提供者配置
	UpdateProvider(ctx context.Context, id string, config *IDProviderConfig) error
	
	// 获取 ID 提供者
	GetProvider(ctx context.Context, id string) (*IDProviderConfig, error)
	
	// 列出所有 ID 提供者
	ListProviders(ctx context.Context) ([]*IDProviderConfig, error)
	
	// 启用/禁用 ID 提供者
	EnableProvider(ctx context.Context, id string) error
	DisableProvider(ctx context.Context, id string) error
	
	// 获取活跃的认证器列表
	GetActiveAuthenticators(ctx context.Context) ([]Authenticator, error)
	
	// 验证 ID 提供者配置
	ValidateProviderConfig(ctx context.Context, config *IDProviderConfig) error
	
	// 测试 ID 提供者连接
	TestProvider(ctx context.Context, id string) error
	
	// 重新加载所有提供者
	ReloadProviders(ctx context.Context) error
}

// DefaultIDProviderManager 默认 ID 提供者管理器实现
type DefaultIDProviderManager struct {
	mu        sync.RWMutex
	providers map[string]*IDProviderConfig
	logger    *telemetry.Logger
	
	// 认证器工厂
	authenticatorFactories map[IDProviderType]AuthenticatorFactory
}

// AuthenticatorFactory 认证器工厂接口
type AuthenticatorFactory interface {
	CreateAuthenticator(config *IDProviderConfig, logger *telemetry.Logger) (Authenticator, error)
	ValidateConfig(config map[string]interface{}) error
	GetConfigSchema() map[string]interface{}
}

// NewDefaultIDProviderManager 创建默认 ID 提供者管理器
func NewDefaultIDProviderManager(logger *telemetry.Logger) *DefaultIDProviderManager {
	manager := &DefaultIDProviderManager{
		providers:              make(map[string]*IDProviderConfig),
		logger:                 logger.With("component", "idprovider_manager"),
		authenticatorFactories: make(map[IDProviderType]AuthenticatorFactory),
	}
	
	// 注册内置认证器工厂
	manager.registerBuiltinFactories()
	
	return manager
}

// registerBuiltinFactories 注册内置认证器工厂
func (m *DefaultIDProviderManager) registerBuiltinFactories() {
	// JWT 认证器工厂
	m.authenticatorFactories[IDProviderTypeJWT] = &JWTAuthenticatorFactory{}
	
	// OIDC 认证器工厂
	m.authenticatorFactories[IDProviderTypeOIDC] = &OIDCAuthenticatorFactory{}
	
	// API Key 认证器工厂
	m.authenticatorFactories[IDProviderTypeAPIKey] = &APIKeyAuthenticatorFactory{}
	
	// mTLS 认证器工厂
	m.authenticatorFactories[IDProviderTypeMTLS] = &MTLSAuthenticatorFactory{}
	
	m.logger.Info("已注册内置认证器工厂", "count", len(m.authenticatorFactories))
}

// RegisterProvider 注册 ID 提供者
func (m *DefaultIDProviderManager) RegisterProvider(ctx context.Context, config *IDProviderConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 验证配置
	if err := m.validateProviderConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 检查是否已存在
	if _, exists := m.providers[config.ID]; exists {
		return fmt.Errorf("ID 提供者 %s 已存在", config.ID)
	}
	
	// 创建认证器实例
	factory, exists := m.authenticatorFactories[config.Type]
	if !exists {
		return fmt.Errorf("不支持的 ID 提供者类型: %s", config.Type)
	}
	
	authenticator, err := factory.CreateAuthenticator(config, m.logger)
	if err != nil {
		return fmt.Errorf("创建认证器失败: %w", err)
	}
	
	// 设置时间戳
	now := time.Now()
	config.CreatedAt = now
	config.UpdatedAt = now
	config.authenticator = authenticator
	
	// 存储配置
	m.providers[config.ID] = config
	
	m.logger.Info("已注册 ID 提供者",
		"id", config.ID,
		"name", config.Name,
		"type", config.Type,
		"enabled", config.Enabled)
	
	return nil
}

// UnregisterProvider 注销 ID 提供者
func (m *DefaultIDProviderManager) UnregisterProvider(ctx context.Context, id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	provider, exists := m.providers[id]
	if !exists {
		return fmt.Errorf("ID 提供者 %s 不存在", id)
	}
	
	// 删除提供者
	delete(m.providers, id)
	
	m.logger.Info("已注销 ID 提供者",
		"id", id,
		"name", provider.Name,
		"type", provider.Type)
	
	return nil
}

// UpdateProvider 更新 ID 提供者配置
func (m *DefaultIDProviderManager) UpdateProvider(ctx context.Context, id string, config *IDProviderConfig) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 检查是否存在
	existingProvider, exists := m.providers[id]
	if !exists {
		return fmt.Errorf("ID 提供者 %s 不存在", id)
	}
	
	// 验证新配置
	if err := m.validateProviderConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}
	
	// 保持原有的创建时间和 ID
	config.ID = id
	config.CreatedAt = existingProvider.CreatedAt
	config.UpdatedAt = time.Now()
	
	// 重新创建认证器实例
	factory, exists := m.authenticatorFactories[config.Type]
	if !exists {
		return fmt.Errorf("不支持的 ID 提供者类型: %s", config.Type)
	}
	
	authenticator, err := factory.CreateAuthenticator(config, m.logger)
	if err != nil {
		return fmt.Errorf("创建认证器失败: %w", err)
	}
	
	config.authenticator = authenticator
	
	// 更新配置
	m.providers[id] = config
	
	m.logger.Info("已更新 ID 提供者",
		"id", id,
		"name", config.Name,
		"type", config.Type,
		"enabled", config.Enabled)
	
	return nil
}

// GetProvider 获取 ID 提供者
func (m *DefaultIDProviderManager) GetProvider(ctx context.Context, id string) (*IDProviderConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	provider, exists := m.providers[id]
	if !exists {
		return nil, fmt.Errorf("ID 提供者 %s 不存在", id)
	}
	
	// 返回副本以避免并发修改
	result := *provider
	result.authenticator = nil // 不暴露内部认证器实例
	
	return &result, nil
}

// ListProviders 列出所有 ID 提供者
func (m *DefaultIDProviderManager) ListProviders(ctx context.Context) ([]*IDProviderConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	providers := make([]*IDProviderConfig, 0, len(m.providers))
	for _, provider := range m.providers {
		// 返回副本以避免并发修改
		result := *provider
		result.authenticator = nil // 不暴露内部认证器实例
		providers = append(providers, &result)
	}
	
	return providers, nil
}

// EnableProvider 启用 ID 提供者
func (m *DefaultIDProviderManager) EnableProvider(ctx context.Context, id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	provider, exists := m.providers[id]
	if !exists {
		return fmt.Errorf("ID 提供者 %s 不存在", id)
	}
	
	provider.Enabled = true
	provider.Status = IDProviderStatusActive
	provider.UpdatedAt = time.Now()
	
	m.logger.Info("已启用 ID 提供者", "id", id, "name", provider.Name)
	
	return nil
}

// DisableProvider 禁用 ID 提供者
func (m *DefaultIDProviderManager) DisableProvider(ctx context.Context, id string) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	provider, exists := m.providers[id]
	if !exists {
		return fmt.Errorf("ID 提供者 %s 不存在", id)
	}
	
	provider.Enabled = false
	provider.Status = IDProviderStatusInactive
	provider.UpdatedAt = time.Now()
	
	m.logger.Info("已禁用 ID 提供者", "id", id, "name", provider.Name)

	return nil
}

// GetActiveAuthenticators 获取活跃的认证器列表
func (m *DefaultIDProviderManager) GetActiveAuthenticators(ctx context.Context) ([]Authenticator, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	var authenticators []Authenticator
	for _, provider := range m.providers {
		if provider.Enabled && provider.Status == IDProviderStatusActive && provider.authenticator != nil {
			authenticators = append(authenticators, provider.authenticator)
		}
	}

	// 按优先级排序
	for i := 0; i < len(authenticators)-1; i++ {
		for j := i + 1; j < len(authenticators); j++ {
			if authenticators[i].Priority() > authenticators[j].Priority() {
				authenticators[i], authenticators[j] = authenticators[j], authenticators[i]
			}
		}
	}

	return authenticators, nil
}

// ValidateProviderConfig 验证 ID 提供者配置
func (m *DefaultIDProviderManager) ValidateProviderConfig(ctx context.Context, config *IDProviderConfig) error {
	return m.validateProviderConfig(config)
}

// validateProviderConfig 内部配置验证方法
func (m *DefaultIDProviderManager) validateProviderConfig(config *IDProviderConfig) error {
	if config == nil {
		return fmt.Errorf("配置不能为空")
	}

	if config.ID == "" {
		return fmt.Errorf("ID 不能为空")
	}

	if config.Name == "" {
		return fmt.Errorf("名称不能为空")
	}

	if config.Type == "" {
		return fmt.Errorf("类型不能为空")
	}

	// 验证类型是否支持
	factory, exists := m.authenticatorFactories[config.Type]
	if !exists {
		return fmt.Errorf("不支持的 ID 提供者类型: %s", config.Type)
	}

	// 验证具体配置
	if err := factory.ValidateConfig(config.Config); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	return nil
}

// TestProvider 测试 ID 提供者连接
func (m *DefaultIDProviderManager) TestProvider(ctx context.Context, id string) error {
	m.mu.RLock()
	provider, exists := m.providers[id]
	m.mu.RUnlock()

	if !exists {
		return fmt.Errorf("ID 提供者 %s 不存在", id)
	}

	if provider.authenticator == nil {
		return fmt.Errorf("ID 提供者 %s 认证器未初始化", id)
	}

	// 这里可以实现具体的连接测试逻辑
	// 例如对于 OIDC 可以测试 discovery endpoint
	// 对于 LDAP 可以测试连接

	m.logger.Info("测试 ID 提供者连接", "id", id, "name", provider.Name)

	return nil
}

// ReloadProviders 重新加载所有提供者
func (m *DefaultIDProviderManager) ReloadProviders(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	var errors []error

	for id, provider := range m.providers {
		if !provider.Enabled {
			continue
		}

		// 重新创建认证器实例
		factory, exists := m.authenticatorFactories[provider.Type]
		if !exists {
			errors = append(errors, fmt.Errorf("ID 提供者 %s: 不支持的类型 %s", id, provider.Type))
			continue
		}

		authenticator, err := factory.CreateAuthenticator(provider, m.logger)
		if err != nil {
			errors = append(errors, fmt.Errorf("ID 提供者 %s: 重新创建认证器失败: %w", id, err))
			provider.Status = IDProviderStatusError
			continue
		}

		provider.authenticator = authenticator
		provider.Status = IDProviderStatusActive
		provider.UpdatedAt = time.Now()

		m.logger.Info("已重新加载 ID 提供者", "id", id, "name", provider.Name)
	}

	if len(errors) > 0 {
		return fmt.Errorf("重新加载部分提供者失败: %v", errors)
	}

	return nil
}

// RegisterAuthenticatorFactory 注册认证器工厂
func (m *DefaultIDProviderManager) RegisterAuthenticatorFactory(providerType IDProviderType, factory AuthenticatorFactory) {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.authenticatorFactories[providerType] = factory
	m.logger.Info("已注册认证器工厂", "type", providerType)
}

// GetSupportedProviderTypes 获取支持的提供者类型
func (m *DefaultIDProviderManager) GetSupportedProviderTypes() []IDProviderType {
	m.mu.RLock()
	defer m.mu.RUnlock()

	types := make([]IDProviderType, 0, len(m.authenticatorFactories))
	for providerType := range m.authenticatorFactories {
		types = append(types, providerType)
	}

	return types
}

// GetProviderConfigSchema 获取提供者配置模式
func (m *DefaultIDProviderManager) GetProviderConfigSchema(providerType IDProviderType) (map[string]interface{}, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	factory, exists := m.authenticatorFactories[providerType]
	if !exists {
		return nil, fmt.Errorf("不支持的 ID 提供者类型: %s", providerType)
	}

	return factory.GetConfigSchema(), nil
}
