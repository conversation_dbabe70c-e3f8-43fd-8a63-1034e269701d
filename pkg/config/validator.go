package config

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

// DefaultConfigValidator implements the ConfigValidator interface
type DefaultConfigValidator struct{}

// NewDefaultConfigValidator creates a new default configuration validator
func NewDefaultConfigValidator() ConfigValidator {
	return &DefaultConfigValidator{}
}

// ValidateConfig validates the complete configuration
func (v *DefaultConfigValidator) ValidateConfig(config *Config) error {
	if config == nil {
		return fmt.Errorf("configuration cannot be nil")
	}
	
	// Validate server configuration
	if err := v.validateServerConfig(config.Server); err != nil {
		return fmt.Errorf("server configuration error: %w", err)
	}
	
	// Validate routes
	for i, route := range config.Routes {
		if err := v.ValidateRouteConfig(route); err != nil {
			return fmt.Errorf("route %d configuration error: %w", i, err)
		}
	}
	
	// Validate security configuration
	if err := v.ValidateSecurityConfig(config.Security); err != nil {
		return fmt.Errorf("security configuration error: %w", err)
	}

	// Validate authentication configuration
	if err := v.ValidateAuthConfig(config.Auth); err != nil {
		return fmt.Errorf("authentication configuration error: %w", err)
	}
	
	// Validate plugin configuration
	if err := v.ValidatePluginConfig(config.Plugins); err != nil {
		return fmt.Errorf("plugin configuration error: %w", err)
	}
	
	// Validate auth configuration
	if err := v.validateAuthConfig(config.Auth); err != nil {
		return fmt.Errorf("auth configuration error: %w", err)
	}
	
	// Validate discovery configuration
	if err := v.validateDiscoveryConfig(config.Discovery); err != nil {
		return fmt.Errorf("discovery configuration error: %w", err)
	}
	
	return nil
}

// ValidateRouteConfig validates a route configuration
func (v *DefaultConfigValidator) ValidateRouteConfig(route RouteConfig) error {
	// Validate route name
	if route.Name == "" {
		return fmt.Errorf("route name is required")
	}
	
	// Validate route path
	if route.Path == "" {
		return fmt.Errorf("route path is required")
	}
	
	if !strings.HasPrefix(route.Path, "/") {
		return fmt.Errorf("route path must start with '/'")
	}
	
	// Validate HTTP method
	validMethods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "*"}
	if !contains(validMethods, route.Method) {
		return fmt.Errorf("invalid HTTP method: %s", route.Method)
	}
	
	// Validate upstream configuration
	if err := v.validateUpstreamConfig(route.Upstream); err != nil {
		return fmt.Errorf("upstream configuration error: %w", err)
	}
	
	// Validate timeout
	if route.Timeout < 0 {
		return fmt.Errorf("timeout cannot be negative")
	}
	
	// Validate retries
	if route.Retries < 0 {
		return fmt.Errorf("retries cannot be negative")
	}
	
	// Validate rewrite configuration
	if route.Rewrite.Enabled {
		if route.Rewrite.From == "" || route.Rewrite.To == "" {
			return fmt.Errorf("rewrite from and to patterns are required when rewrite is enabled")
		}
		
		// Validate regex pattern
		if _, err := regexp.Compile(route.Rewrite.From); err != nil {
			return fmt.Errorf("invalid rewrite from pattern: %w", err)
		}
	}
	
	return nil
}

// ValidateSecurityConfig validates security configuration
func (v *DefaultConfigValidator) ValidateSecurityConfig(security SecurityConfig) error {
	// Validate rate limiting configuration
	if security.RateLimit.Enabled {
		if err := v.validateRateLimitConfig(security.RateLimit); err != nil {
			return fmt.Errorf("rate limit configuration error: %w", err)
		}
	}
	
	// Validate CORS configuration
	if security.CORS.Enabled {
		if err := v.validateCORSConfig(security.CORS); err != nil {
			return fmt.Errorf("CORS configuration error: %w", err)
		}
	}
	
	// Validate WAF configuration
	if security.WAF.Enabled {
		if err := v.validateWAFConfig(security.WAF); err != nil {
			return fmt.Errorf("WAF configuration error: %w", err)
		}
	}
	
	// Validate IP filter configuration
	if security.IPFilter.Enabled {
		if err := v.validateIPFilterConfig(security.IPFilter); err != nil {
			return fmt.Errorf("IP filter configuration error: %w", err)
		}
	}
	
	return nil
}

// ValidatePluginConfig validates plugin configuration
func (v *DefaultConfigValidator) ValidatePluginConfig(plugins PluginConfig) error {
	// Validate plugin directory
	if plugins.Directory == "" {
		return fmt.Errorf("plugin directory is required")
	}
	
	// Validate individual plugin configurations
	for name, config := range plugins.Plugins {
		if name == "" {
			return fmt.Errorf("plugin name cannot be empty")
		}
		
		// Basic validation for plugin config structure
		if configMap, ok := config.(map[string]interface{}); ok {
			if enabled, exists := configMap["enabled"]; exists {
				if _, ok := enabled.(bool); !ok {
					return fmt.Errorf("plugin %s: enabled must be a boolean", name)
				}
			}
			
			if priority, exists := configMap["priority"]; exists {
				if _, ok := priority.(int); !ok {
					if priorityFloat, ok := priority.(float64); ok {
						// JSON unmarshaling converts numbers to float64
						configMap["priority"] = int(priorityFloat)
					} else {
						return fmt.Errorf("plugin %s: priority must be an integer", name)
					}
				}
			}
		}
	}
	
	return nil
}

// validateServerConfig validates server configuration
func (v *DefaultConfigValidator) validateServerConfig(server ServerConfig) error {
	if server.Address == "" {
		return fmt.Errorf("server address is required")
	}
	
	if server.ReadTimeout < 0 {
		return fmt.Errorf("read timeout cannot be negative")
	}
	
	if server.WriteTimeout < 0 {
		return fmt.Errorf("write timeout cannot be negative")
	}
	
	if server.IdleTimeout < 0 {
		return fmt.Errorf("idle timeout cannot be negative")
	}
	
	// Validate TLS configuration
	if server.TLS.Enabled {
		if server.TLS.CertFile == "" {
			return fmt.Errorf("TLS cert file is required when TLS is enabled")
		}
		if server.TLS.KeyFile == "" {
			return fmt.Errorf("TLS key file is required when TLS is enabled")
		}
	}
	
	return nil
}

// validateUpstreamConfig validates upstream configuration
func (v *DefaultConfigValidator) validateUpstreamConfig(upstream UpstreamConfig) error {
	validTypes := []string{"static", "discovery"}
	if !contains(validTypes, upstream.Type) {
		return fmt.Errorf("invalid upstream type: %s", upstream.Type)
	}
	
	validLBAlgorithms := []string{"round_robin", "weighted_round_robin", "least_connections", "ip_hash"}
	if !contains(validLBAlgorithms, upstream.LoadBalancer) {
		return fmt.Errorf("invalid load balancer algorithm: %s", upstream.LoadBalancer)
	}
	
	if upstream.Type == "static" {
		if len(upstream.Servers) == 0 {
			return fmt.Errorf("at least one server is required for static upstream")
		}
		
		for i, server := range upstream.Servers {
			if server.Host == "" {
				return fmt.Errorf("server %d: host is required", i)
			}
			if server.Port <= 0 || server.Port > 65535 {
				return fmt.Errorf("server %d: invalid port %d", i, server.Port)
			}
			if server.Weight < 0 {
				return fmt.Errorf("server %d: weight cannot be negative", i)
			}
		}
	} else if upstream.Type == "discovery" {
		if upstream.ServiceName == "" {
			return fmt.Errorf("service name is required for discovery upstream")
		}
	}
	
	// Validate health check configuration
	if upstream.HealthCheck.Enabled {
		if upstream.HealthCheck.Path == "" {
			return fmt.Errorf("health check path is required when health check is enabled")
		}
		if upstream.HealthCheck.Interval <= 0 {
			return fmt.Errorf("health check interval must be positive")
		}
		if upstream.HealthCheck.Timeout <= 0 {
			return fmt.Errorf("health check timeout must be positive")
		}
		if upstream.HealthCheck.Retries < 0 {
			return fmt.Errorf("health check retries cannot be negative")
		}
	}
	
	return nil
}

// validateRateLimitConfig validates rate limiting configuration
func (v *DefaultConfigValidator) validateRateLimitConfig(rateLimit RateLimitConfig) error {
	validAlgorithms := []string{"token_bucket", "leaky_bucket", "fixed_window", "sliding_window"}
	if !contains(validAlgorithms, rateLimit.Algorithm) {
		return fmt.Errorf("invalid rate limit algorithm: %s", rateLimit.Algorithm)
	}
	
	for i, rule := range rateLimit.Rules {
		if rule.Path == "" {
			return fmt.Errorf("rate limit rule %d: path is required", i)
		}
		if rule.Rate <= 0 {
			return fmt.Errorf("rate limit rule %d: rate must be positive", i)
		}
		if rule.Burst < 0 {
			return fmt.Errorf("rate limit rule %d: burst cannot be negative", i)
		}
		
		// Validate window duration
		if rule.Window < 0 {
			return fmt.Errorf("rate limit rule %d: window duration cannot be negative", i)
		}
		
		validKeyBy := []string{"ip", "user", "api_key", "header"}
		if rule.KeyBy != "" && !contains(validKeyBy, rule.KeyBy) {
			return fmt.Errorf("rate limit rule %d: invalid key_by value: %s", i, rule.KeyBy)
		}
	}
	
	return nil
}

// validateCORSConfig validates CORS configuration
func (v *DefaultConfigValidator) validateCORSConfig(cors CORSConfig) error {
	// Validate allowed origins
	for i, origin := range cors.AllowedOrigins {
		if origin != "*" {
			if _, err := url.Parse(origin); err != nil {
				return fmt.Errorf("CORS allowed origin %d: invalid URL: %w", i, err)
			}
		}
	}
	
	// Validate max age
	if cors.MaxAge < 0 {
		return fmt.Errorf("CORS max age cannot be negative")
	}
	
	return nil
}

// validateWAFConfig validates WAF configuration
func (v *DefaultConfigValidator) validateWAFConfig(waf WAFConfig) error {
	for i, rule := range waf.Rules {
		if rule.Name == "" {
			return fmt.Errorf("WAF rule %d: name is required", i)
		}
		if rule.Pattern == "" {
			return fmt.Errorf("WAF rule %d: pattern is required", i)
		}
		
		// Validate regex pattern
		if _, err := regexp.Compile(rule.Pattern); err != nil {
			return fmt.Errorf("WAF rule %d: invalid pattern: %w", i, err)
		}
		
		validActions := []string{"block", "log", "challenge"}
		if !contains(validActions, rule.Action) {
			return fmt.Errorf("WAF rule %d: invalid action: %s", i, rule.Action)
		}
	}
	
	return nil
}

// validateIPFilterConfig validates IP filter configuration
func (v *DefaultConfigValidator) validateIPFilterConfig(ipFilter IPFilterConfig) error {
	// Validate whitelist IPs
	for i, ip := range ipFilter.Whitelist {
		if !isValidIPOrCIDR(ip) {
			return fmt.Errorf("IP filter whitelist %d: invalid IP or CIDR: %s", i, ip)
		}
	}
	
	// Validate blacklist IPs
	for i, ip := range ipFilter.Blacklist {
		if !isValidIPOrCIDR(ip) {
			return fmt.Errorf("IP filter blacklist %d: invalid IP or CIDR: %s", i, ip)
		}
	}
	
	return nil
}

// validateAuthConfig validates authentication configuration
func (v *DefaultConfigValidator) validateAuthConfig(auth AuthConfig) error {
	// Validate JWT configuration
	if auth.JWT.Enabled {
		if auth.JWT.Secret == "" {
			return fmt.Errorf("JWT secret is required when JWT is enabled")
		}
		
		validAlgorithms := []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512"}
		if !contains(validAlgorithms, auth.JWT.Algorithm) {
			return fmt.Errorf("invalid JWT algorithm: %s", auth.JWT.Algorithm)
		}
		
		if auth.JWT.Expiration <= 0 {
			return fmt.Errorf("JWT expiration must be positive")
		}
	}
	
	// Validate API key configuration
	if auth.APIKey.Enabled {
		if auth.APIKey.HeaderName == "" && auth.APIKey.QueryParam == "" {
			return fmt.Errorf("API key header name or query param is required when API key auth is enabled")
		}
	}
	
	// Validate OIDC configuration
	if auth.OIDC.Enabled {
		if auth.OIDC.Issuer == "" {
			return fmt.Errorf("OIDC issuer is required when OIDC is enabled")
		}
		if auth.OIDC.ClientID == "" {
			return fmt.Errorf("OIDC client ID is required when OIDC is enabled")
		}
		if auth.OIDC.ClientSecret == "" {
			return fmt.Errorf("OIDC client secret is required when OIDC is enabled")
		}
		
		// Validate issuer URL
		if _, err := url.Parse(auth.OIDC.Issuer); err != nil {
			return fmt.Errorf("invalid OIDC issuer URL: %w", err)
		}
	}
	
	return nil
}

// validateDiscoveryConfig validates service discovery configuration
func (v *DefaultConfigValidator) validateDiscoveryConfig(discovery DiscoveryConfig) error {
	validTypes := []string{"consul", "nacos", "eureka"}
	if discovery.Type != "" && !contains(validTypes, discovery.Type) {
		return fmt.Errorf("invalid discovery type: %s", discovery.Type)
	}
	
	// Validate Consul configuration
	if discovery.Type == "consul" {
		if discovery.Consul.Address == "" {
			return fmt.Errorf("Consul address is required")
		}
	}
	
	// Validate Nacos configuration
	if discovery.Type == "nacos" {
		if len(discovery.Nacos.ServerConfigs) == 0 {
			return fmt.Errorf("at least one Nacos server config is required")
		}
		
		for i, server := range discovery.Nacos.ServerConfigs {
			if server.IpAddr == "" {
				return fmt.Errorf("Nacos server %d: IP address is required", i)
			}
			if server.Port <= 0 || server.Port > 65535 {
				return fmt.Errorf("Nacos server %d: invalid port %d", i, server.Port)
			}
		}
	}
	
	return nil
}

// ValidateAuthConfig 验证认证配置
func (v *DefaultConfigValidator) ValidateAuthConfig(auth AuthConfig) error {
	// 验证 JWT 配置
	if auth.JWT.Enabled {
		if err := v.validateJWTConfig(auth.JWT); err != nil {
			return fmt.Errorf("JWT configuration error: %w", err)
		}
	}

	// 验证 OIDC 配置
	if auth.OIDC.Enabled {
		if err := v.validateOIDCConfig(auth.OIDC); err != nil {
			return fmt.Errorf("OIDC configuration error: %w", err)
		}
	}

	// 验证 API Key 配置
	if auth.APIKey.Enabled {
		if err := v.validateAPIKeyConfig(auth.APIKey); err != nil {
			return fmt.Errorf("API Key configuration error: %w", err)
		}
	}

	// 验证 mTLS 配置
	if auth.MTLS.Enabled {
		if err := v.validateMTLSConfig(auth.MTLS); err != nil {
			return fmt.Errorf("mTLS configuration error: %w", err)
		}
	}

	// 验证策略配置
	if err := v.validatePolicyConfig(auth.Policies); err != nil {
		return fmt.Errorf("policy configuration error: %w", err)
	}

	return nil
}

// validateJWTConfig 验证 JWT 配置
func (v *DefaultConfigValidator) validateJWTConfig(jwt JWTConfig) error {
	if jwt.Secret == "" && jwt.PublicKey == "" {
		return fmt.Errorf("JWT configuration must have either secret or public_key")
	}

	if jwt.Algorithm == "" {
		return fmt.Errorf("JWT algorithm is required")
	}

	validAlgorithms := []string{"HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "ES256", "ES384", "ES512"}
	valid := false
	for _, alg := range validAlgorithms {
		if jwt.Algorithm == alg {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("unsupported JWT algorithm: %s", jwt.Algorithm)
	}

	if jwt.Expiration <= 0 {
		return fmt.Errorf("JWT expiration must be positive")
	}

	return nil
}

// validateOIDCConfig 验证 OIDC 配置
func (v *DefaultConfigValidator) validateOIDCConfig(oidc OIDCConfig) error {
	if oidc.Issuer == "" {
		return fmt.Errorf("OIDC issuer is required")
	}

	if oidc.ClientID == "" {
		return fmt.Errorf("OIDC client_id is required")
	}

	if oidc.ClientSecret == "" {
		return fmt.Errorf("OIDC client_secret is required")
	}

	// 验证 issuer URL 格式
	if _, err := url.Parse(oidc.Issuer); err != nil {
		return fmt.Errorf("invalid OIDC issuer URL: %w", err)
	}

	// 验证 redirect URL 格式（如果提供）
	if oidc.RedirectURL != "" {
		if _, err := url.Parse(oidc.RedirectURL); err != nil {
			return fmt.Errorf("invalid OIDC redirect URL: %w", err)
		}
	}

	return nil
}

// validateAPIKeyConfig 验证 API Key 配置
func (v *DefaultConfigValidator) validateAPIKeyConfig(apiKey APIKeyConfig) error {
	if apiKey.HeaderName == "" && apiKey.QueryParam == "" {
		return fmt.Errorf("API Key configuration must specify either header_name or query_param")
	}

	return nil
}

// validateMTLSConfig 验证 mTLS 配置
func (v *DefaultConfigValidator) validateMTLSConfig(mtls MTLSConfig) error {
	if mtls.CAFile == "" {
		return fmt.Errorf("mTLS ca_file is required")
	}

	if mtls.OCSPTimeoutSeconds < 0 {
		return fmt.Errorf("mTLS OCSP timeout cannot be negative")
	}

	return nil
}

// validatePolicyConfig 验证策略配置
func (v *DefaultConfigValidator) validatePolicyConfig(policies PolicyConfig) error {
	if policies.Engine == "" {
		return fmt.Errorf("policy engine is required")
	}

	validEngines := []string{"builtin", "opa"}
	valid := false
	for _, engine := range validEngines {
		if policies.Engine == engine {
			valid = true
			break
		}
	}
	if !valid {
		return fmt.Errorf("unsupported policy engine: %s", policies.Engine)
	}

	// 验证策略规则
	for i, rule := range policies.Policies {
		if err := v.validatePolicyRule(rule, i); err != nil {
			return err
		}
	}

	return nil
}

// validatePolicyRule 验证策略规则
func (v *DefaultConfigValidator) validatePolicyRule(rule PolicyRule, index int) error {
	if rule.Name == "" {
		return fmt.Errorf("policy rule %d: name is required", index)
	}

	if rule.Path == "" {
		return fmt.Errorf("policy rule %d: path is required", index)
	}

	if rule.Method == "" {
		return fmt.Errorf("policy rule %d: method is required", index)
	}

	return nil
}

// Helper functions

func isValidIPOrCIDR(ip string) bool {
	// This is a simplified validation - in production, use proper IP validation
	return ip != "" && (strings.Contains(ip, ".") || strings.Contains(ip, ":"))
}
